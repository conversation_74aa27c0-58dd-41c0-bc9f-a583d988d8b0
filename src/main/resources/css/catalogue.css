.vehicle-card {
    -fx-background-color: white;
    -fx-background-radius: 16;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 10, 0, 0, 2);
    -fx-border-color: #e2e8f0;
    -fx-border-radius: 16;
    -fx-border-width: 1;
    -fx-padding: 0;
    -fx-cursor: hand;
}

.vehicle-card-hover {
    -fx-effect: dropshadow(gaussian, rgba(59,130,246,0.15), 15, 0, 0, 5);
    -fx-translate-y: -2;
}

.status-badge {
    -fx-background-radius: 12;
    -fx-padding: 4 12;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.list-row {
    -fx-padding: 10;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-background-color: white;
}

.list-row-hover {
    -fx-background-color: #f8f9fa;
}

.detail-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-background-radius: 4;
    -fx-padding: 5 8;
}

.rent-button {
    -fx-background-color: #3b82f6;
    -fx-text-fill: white;
    -fx-background-radius: 8;
    -fx-padding: 10 24;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.image-container {
    -fx-background-color: #f8fafc;
    -fx-background-radius: 16 16 0 0;
    -fx-padding: 20;
}

.vehicle-image {
    -fx-background-radius: 12;
}

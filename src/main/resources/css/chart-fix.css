/* Fix for JavaFX 22.0.1 chart CSS warnings */
/* This file overrides problematic chart styles to prevent ClassCastException warnings */

.chart-bar {
    -fx-background-color: #3b82f6;
    -fx-border-color: #1d4ed8;
    -fx-border-width: 1px;
}

.chart-pie {
    -fx-background-color: #10b981;
    -fx-border-color: #059669;
    -fx-border-width: 1px;
}

.chart-line-symbol {
    -fx-background-color: #f59e0b;
    -fx-background-radius: 3px;
}

.chart-series-line {
    -fx-stroke: #8b5cf6;
    -fx-stroke-width: 2px;
}

/* Override any problematic gradient styles */
.chart-content {
    -fx-background-color: white;
}

.chart-plot-background {
    -fx-background-color: #f8fafc;
}

.chart-vertical-grid-lines,
.chart-horizontal-grid-lines {
    -fx-stroke: #e5e7eb;
    -fx-stroke-width: 1px;
}

.chart-alternative-row-fill {
    -fx-fill: #f9fafb;
}

.chart-legend {
    -fx-background-color: white;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-padding: 8px;
}

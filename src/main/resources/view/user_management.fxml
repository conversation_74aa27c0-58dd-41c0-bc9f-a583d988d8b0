<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;" vbarPolicy="AS_NEEDED" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.UserManagementController">
    <content>
        <VBox prefHeight="1400" prefWidth="1911.0" spacing="28.0" style="-fx-background-color: #f8fafc;">
            <padding><Insets bottom="200.0" left="28.0" right="28.0" top="28.0" /></padding>

            <!-- Header Section with Enhanced Styling -->
            <VBox spacing="16.0" style="-fx-background-color: white; -fx-background-radius: 16; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);">
                <padding><Insets bottom="24.0" left="24.0" right="24.0" top="24.0" /></padding>
                <HBox alignment="CENTER_LEFT" spacing="24.0">
                    <VBox spacing="8.0">
                        <Label style="-fx-font-size: 26px; -fx-font-weight: 800; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="👥 Gestion des Utilisateurs" />
                        <Label style="-fx-font-size: 15px; -fx-text-fill: #64748b; -fx-font-family: 'Inter', 'Segoe UI';" text="Gérez les comptes utilisateurs et les permissions" />
                    </VBox>
                    <Region HBox.hgrow="ALWAYS" />
                    <HBox spacing="12.0">
                        <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-font-size: 14px; -fx-background-radius: 12; -fx-padding: 12 20; -fx-font-family: 'Inter', 'Segoe UI'; -fx-font-weight: 600; -fx-border-color: #e2e8f0; -fx-border-width: 1; -fx-border-radius: 12;" text="🔄 Actualiser" />
                        <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-size: 14px; -fx-background-radius: 12; -fx-padding: 12 20; -fx-font-family: 'Inter', 'Segoe UI'; -fx-font-weight: 600; -fx-effect: dropshadow(gaussian, rgba(16,185,129,0.3), 8, 0, 0, 4);" text="📊 Exporter" />
                    </HBox>
                </HBox>
            </VBox>

            <!-- Main Content Area -->
            <HBox spacing="24.0">
                <!-- Left Panel - User List -->
                <VBox prefWidth="700.0" spacing="20.0" HBox.hgrow="ALWAYS">
                    <!-- Search Card -->
                    <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);">
                        <Label style="-fx-font-size: 18px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="🔍 Recherche Utilisateurs" />
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                            <TextField fx:id="searchField" prefHeight="44.0" prefWidth="400.0" promptText="Rechercher par nom d'utilisateur, rôle..." style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 10; -fx-padding: 12; -fx-font-size: 14px; -fx-prompt-text-fill: #64748b;" />
                            <Button fx:id="btnSearch" onAction="#handleSearch" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 16; -fx-font-weight: 600; -fx-effect: dropshadow(gaussian, rgba(59,130,246,0.3), 8, 0, 0, 2);" text="🔍" />
                            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #64748b; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 16; -fx-font-weight: 600;" text="✨ Effacer" />
                        </HBox>
                    </VBox>

                    <!-- User Table Card -->
                    <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);" VBox.vgrow="ALWAYS">
                        <HBox alignment="CENTER_LEFT" spacing="16.0">
                            <Label style="-fx-font-size: 18px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="📋 Liste des Utilisateurs" />
                            <Region HBox.hgrow="ALWAYS" />
                            <Label fx:id="lblTotalCount" style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-family: 'Inter', 'Segoe UI'; -fx-background-color: #f1f5f9; -fx-padding: 6 12; -fx-background-radius: 20;" text="Total: 0" />
                        </HBox>

                        <TableView fx:id="userTable" prefHeight="400.0" style="-fx-background-color: transparent; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-table-header-border-color: transparent; -fx-control-inner-background: white;">
                            <columns>
                                <TableColumn fx:id="idColumn" prefWidth="60.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="ID" />
                                <TableColumn fx:id="usernameColumn" prefWidth="303.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="Nom d'utilisateur" />
                                <TableColumn fx:id="roleColumn" prefWidth="183.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="Rôle" />
                                <TableColumn fx:id="emailColumn" prefWidth="228.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="Email" />
                                <TableColumn fx:id="statusColumn" prefWidth="410.0" style="-fx-text-fill: #1e293b; -fx-font-weight: 600; -fx-font-size: 13px;" text="Statut" />
                            </columns>
                        </TableView>

                        <!-- Action Buttons -->
                        <HBox alignment="CENTER" spacing="8.0">
                            <Button fx:id="btnAddUser" onAction="#handleSave" style="-fx-background-color: #1e293b; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(30,41,59,0.3), 6, 0, 0, 2);" text="➕ Ajouter" />
                            <Button fx:id="btnUpdateUser" onAction="#handleSave" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(59,130,246,0.3), 6, 0, 0, 2);" text="✏️ Modifier" />
                            <Button fx:id="btnDeleteUser" onAction="#handleDeleteUser" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-weight: 600; -fx-font-size: 12px; -fx-effect: dropshadow(gaussian, rgba(239,68,68,0.3), 6, 0, 0, 2);" text="🗑️ Supprimer" />
                        </HBox>
                    </VBox>
                </VBox>

                <!-- Right Panel - User Details/Form -->
                <VBox prefWidth="400.0" spacing="20.0">
                    <VBox spacing="24.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4);">
                        <!-- Form Header -->
                        <VBox spacing="8.0">
                            <Label style="-fx-font-size: 20px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Inter', 'Segoe UI';" text="📝 Détails Utilisateur" />
                            <Separator style="-fx-background-color: linear-gradient(to right, #3b82f6, #8b5cf6);" />
                        </VBox>

                        <!-- User Form Fields -->
                        <VBox spacing="16.0">
                            <VBox spacing="8.0">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="👤 Nom d'utilisateur" />
                                <TextField fx:id="usernameField" promptText="Entrez le nom d'utilisateur" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14px;" />
                            </VBox>

                            <VBox spacing="8.0">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="🔒 Mot de passe" />
                                <PasswordField fx:id="passwordField" promptText="Entrez le mot de passe" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14px;" />
                            </VBox>

                            <VBox spacing="8.0">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="✉️ Email" />
                                <TextField fx:id="emailField" promptText="Entrez l'email" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14px;" />
                            </VBox>

                            <VBox spacing="8.0">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="👑 Rôle" />
                                <ComboBox fx:id="roleCombo" promptText="Sélectionner le rôle" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;" />
                            </VBox>

                            <VBox spacing="8.0">
                                <Label style="-fx-font-weight: 600; -fx-font-size: 14px; -fx-text-fill: #374151; -fx-font-family: 'Inter', 'Segoe UI';" text="🟢 Statut" />
                                <ComboBox fx:id="statusCombo" promptText="Sélectionner le statut" style="-fx-background-color: #f8fafc; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-font-size: 14px;" />
                            </VBox>
                        </VBox>

                        <!-- Form Buttons -->
                        <HBox alignment="CENTER" spacing="12.0">
                            <Button fx:id="btnCancel" onAction="#handleCancel" style="-fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 10; -fx-padding: 12 24; -fx-font-weight: 600; -fx-font-size: 14px; -fx-background-radius: 10; -fx-background-color: white;" text="❌ Annuler" />
                            <Button fx:id="btnSave" onAction="#handleSave" style="-fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 12 24; -fx-font-weight: 600; -fx-font-size: 14px; -fx-background-color: #10b981; -fx-effect: dropshadow(gaussian, rgba(16,185,129,0.3), 8, 0, 0, 4);" text="💾 Enregistrer" />
                        </HBox>
                    </VBox>
                </VBox>
            </HBox>
        </VBox>
    </content>
</ScrollPane>

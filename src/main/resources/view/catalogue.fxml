<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.CatalogueController" stylesheets="@../css/catalogue.css">
   <top>
      <!-- Modern Header with Gradient -->
      <VBox style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 30;">
         <HBox alignment="CENTER_LEFT" spacing="20">
            <VBox spacing="8">
               <Label style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: bold;" text="🚗 Catalogue des Véhicules" />
               <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 16px;" text="Découvrez notre flotte moderne et choisissez votre véhicule idéal" />
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            <VBox alignment="CENTER_RIGHT" spacing="8">
               <HBox alignment="CENTER_RIGHT" spacing="12">
                  <TextField fx:id="searchField" onKeyReleased="#handleSearch" promptText="🔍 Rechercher par marque, modèle..." style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 25; -fx-padding: 12 20; -fx-font-size: 14px; -fx-min-width: 280;" />
                  <Button fx:id="btnViewMode" onAction="#toggleViewMode" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 25; -fx-padding: 12 20; -fx-font-weight: bold;" text="📋 Vue Liste" />
                  <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 25; -fx-padding: 12 20; -fx-font-weight: bold;" text="🔄 Actualiser" />
               </HBox>
               <Label fx:id="lblTotalCount" style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 14px;" text="Total: 0 véhicules" />
            </VBox>
         </HBox>

         <!-- Advanced Filter Bar -->
         <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 20 0 0 0;">
            <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="Filtres:" />
            <ComboBox fx:id="marqueFilter" onAction="#handleApplyFilters" promptText="Toutes les marques" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 20; -fx-padding: 8 15;" />
            <ComboBox fx:id="etatFilter" onAction="#handleApplyFilters" promptText="Tous les états" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 20; -fx-padding: 8 15;" />
            <ComboBox fx:id="carburantFilter" onAction="#handleApplyFilters" promptText="Tous carburants" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 20; -fx-padding: 8 15;" />
            <TextField fx:id="prixMaxFilter" minHeight="-Infinity" minWidth="-Infinity" onKeyReleased="#handleApplyFilters" prefHeight="33.0" prefWidth="188.0" promptText="Prix max (DH)" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 20; -fx-padding: 8 15; -fx-pref-width: 120;" />
            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: rgba(255,255,255,0.3); -fx-text-fill: white; -fx-background-radius: 20; -fx-padding: 8 15;" text="✖ Effacer" />
         </HBox>
      </VBox>
   </top>

   <center>
      <ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: #f8fafc; -fx-background-color: #f8fafc;" vbarPolicy="AS_NEEDED">
         <VBox spacing="0" style="-fx-background-color: #f8fafc; -fx-padding: 25;">

            <!-- Statistics Cards -->
            <HBox spacing="20" style="-fx-padding: 0 0 25 0;">
               <VBox alignment="CENTER" prefWidth="266.0" spacing="8" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3); -fx-border-color: #e5e7eb; -fx-border-radius: 15; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblAvailableCount" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #10b981;" text="0" />
                  <Label style="-fx-font-size: 14px; -fx-text-fill: #6b7280; -fx-font-weight: 600;" text="Disponibles" />
               </VBox>

               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3); -fx-border-color: #e5e7eb; -fx-border-radius: 15; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblRentedCount" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #f59e0b;" text="0" />
                  <Label style="-fx-font-size: 14px; -fx-text-fill: #6b7280; -fx-font-weight: 600;" text="Loués" />
               </VBox>

               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3); -fx-border-color: #e5e7eb; -fx-border-radius: 15; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblMaintenanceCount" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #ef4444;" text="0" />
                  <Label style="-fx-font-size: 14px; -fx-text-fill: #6b7280; -fx-font-weight: 600;" text="En Panne" />
               </VBox>

               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 3); -fx-border-color: #e5e7eb; -fx-border-radius: 15; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblTotalVehicles" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="0" />
                  <Label style="-fx-font-size: 14px; -fx-text-fill: #6b7280; -fx-font-weight: 600;" text="Total" />
               </VBox>
            </HBox>

            <!-- Main Content Area -->
            <VBox spacing="0" style="-fx-background-color: white; -fx-background-radius: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 15, 0, 0, 5); -fx-border-color: #e5e7eb; -fx-border-radius: 20; -fx-border-width: 1;">

               <!-- Content Header -->
               <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 25 30 20 30; -fx-border-color: #f1f5f9; -fx-border-width: 0 0 1 0;">
                  <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="🚗 Nos Véhicules" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #6366f1; -fx-text-fill: white; -fx-background-radius: 12; -fx-padding: 10 20; -fx-font-weight: bold;" text="📊 Exporter" />
                  <Button fx:id="btnAddVehicle" onAction="#handleAddVehicle" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 12; -fx-padding: 10 20; -fx-font-weight: bold;" text="➕ Ajouter" />
               </HBox>

               <!-- Vehicle Grid/List Container -->
               <StackPane>
                  <!-- Card View (Default) -->
                  <ScrollPane fx:id="cardViewContainer" fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: white; -fx-background-color: white;" vbarPolicy="AS_NEEDED">
                     <FlowPane fx:id="cataloguePane" alignment="TOP_LEFT" hgap="20" style="-fx-background-color: white; -fx-padding: 30;" vgap="20" />
                  </ScrollPane>

                  <!-- List View -->
                  <ScrollPane fx:id="listViewContainer" fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: white; -fx-background-color: white;" vbarPolicy="AS_NEEDED" visible="false">
                     <VBox fx:id="listViewPane" spacing="15" style="-fx-background-color: white; -fx-padding: 30;" />
                  </ScrollPane>
               </StackPane>

               <!-- Empty State -->
               <VBox fx:id="emptyState" alignment="CENTER" spacing="20" style="-fx-padding: 80;" visible="false">
                  <Label style="-fx-font-size: 48px;" text="🔍" />
                  <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #6b7280;" text="Aucun véhicule trouvé" />
                  <Label style="-fx-font-size: 16px; -fx-text-fill: #9ca3af;" text="Essayez de modifier vos critères de recherche" />
                  <Button onAction="#handleClearFilters" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 12; -fx-padding: 12 24; -fx-font-weight: bold;" text="Effacer les filtres" />
               </VBox>
            </VBox>
         </VBox>
      </ScrollPane>
   </center>
</BorderPane>

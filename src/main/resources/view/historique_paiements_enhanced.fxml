<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriquePaiementsEnhancedController">
   <top>
      <VBox style="-fx-background-color: linear-gradient(to right, #10b981, #059669); -fx-padding: 25;">
         <HBox alignment="CENTER_LEFT" spacing="20">
            <Label style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: bold;" text="💳 Historique Détaillé des Paiements" />
            <Region HBox.hgrow="ALWAYS" />
            <MenuButton style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12 20; -fx-font-weight: bold;" text="📊 Exporter">
               <items>
                  <MenuItem fx:id="exportPaymentsCSV" onAction="#handleExportCSV" text="📄 Export CSV" />
                  <MenuItem fx:id="exportPaymentsExcel" onAction="#handleExportExcel" text="📊 Export Excel" />
                  <MenuItem fx:id="exportPaymentsPDF" onAction="#handleExportPDF" text="📋 Rapport PDF" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="exportUnpaid" onAction="#handleExportUnpaid" text="⚠️ Impayés" />
                  <MenuItem fx:id="exportOverdue" onAction="#handleExportOverdue" text="🔴 En Retard" />
               </items>
            </MenuButton>
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12 20; -fx-font-weight: bold;" text="🔄 Actualiser" />
         </HBox>

         <!-- Advanced Filters Section -->
         <VBox spacing="12" style="-fx-padding: 15 0 0 0;">
            <!-- Primary Filters -->
            <HBox alignment="CENTER_LEFT" spacing="15">
               <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="🔍 Filtres:" />
               <ComboBox fx:id="filterStatusCombo" promptText="Statut Paiement" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <ComboBox fx:id="filterMethodCombo" promptText="Méthode" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 120;" />
               <ComboBox fx:id="filterClientCombo" promptText="Client" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 150;" />
               <DatePicker fx:id="filterDateFrom" promptText="Date début" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
               <DatePicker fx:id="filterDateTo" promptText="Date fin" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
            </HBox>

            <!-- Advanced Filters -->
            <HBox alignment="CENTER_LEFT" spacing="15">
               <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="⚙️ Avancés:" />
               <TextField fx:id="filterAmountMin" promptText="Montant min" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-max-width: 100;" />
               <TextField fx:id="filterAmountMax" promptText="Montant max" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-max-width: 100;" />
               <ComboBox fx:id="filterVehicleCombo" promptText="Véhicule" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 140;" />
               <CheckBox fx:id="filterOverdueOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="En retard uniquement" />
               <CheckBox fx:id="filterPartialOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="Paiements partiels" />
               <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: rgba(255,255,255,0.3); -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="✖ Effacer" />
            </HBox>
         </VBox>
      </VBox>
   </top>


   <center>
      <SplitPane dividerPositions="0.75" orientation="HORIZONTAL" prefHeight="678.0" prefWidth="1591.0">
         <!-- Main Content Area -->
         <VBox spacing="15" style="-fx-padding: 20;">
            <!-- Enhanced Statistics Cards -->
            <HBox spacing="15">
               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #dcfce7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #16a34a; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblTotalRevenue" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #15803d;" text="0.00 DH" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #166534; -fx-font-weight: bold;" text="💰 REVENUS TOTAL" />
                  <Label fx:id="lblRevenueChange" style="-fx-font-size: 10px; -fx-text-fill: #15803d;" text="↗ +0% ce mois" />
               </VBox>

               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #dbeafe; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #3b82f6; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblPaidCount" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1d4ed8;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #1e40af; -fx-font-weight: bold;" text="✅ PAIEMENTS RÉALISÉS" />
                  <Label fx:id="lblPaidPercentage" style="-fx-font-size: 10px; -fx-text-fill: #1d4ed8;" text="0% du total" />
               </VBox>

               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #fef3c7; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #f59e0b; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblPendingCount" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #d97706;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #92400e; -fx-font-weight: bold;" text="⏳ EN ATTENTE" />
                  <Label fx:id="lblPendingAmount" style="-fx-font-size: 10px; -fx-text-fill: #d97706;" text="0.00 DH à recevoir" />
               </VBox>

               <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #fee2e2; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #ef4444; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                  <Label fx:id="lblOverdueCount" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #dc2626;" text="0" />
                  <Label style="-fx-font-size: 12px; -fx-text-fill: #991b1b; -fx-font-weight: bold;" text="🔴 EN RETARD" />
                  <Label fx:id="lblOverdueAmount" style="-fx-font-size: 10px; -fx-text-fill: #dc2626;" text="0.00 DH en retard" />
               </VBox>
            </HBox>

            <!-- Payment Methods Chart -->
            <HBox spacing="15">
               <VBox spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="💳 Méthodes de Paiement" />
                  <VBox fx:id="paymentMethodsChart" spacing="8" />
               </VBox>

               <VBox spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📊 Tendances Mensuelles" />
                  <VBox fx:id="monthlyTrendsChart" spacing="8" />
               </VBox>
            </HBox>

            <!-- Enhanced Payments Table -->
            <VBox spacing="12" VBox.vgrow="ALWAYS">
               <HBox alignment="CENTER_LEFT" spacing="15">
                  <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="💳 Historique des Paiements" />
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="searchField" onKeyReleased="#handleSearch" promptText="🔍 Rechercher client, montant..." style="-fx-background-radius: 8; -fx-padding: 8 12; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-min-width: 200;" />
                  <Button fx:id="btnBulkActions" onAction="#handleBulkActions" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16;" text="⚡ Actions Groupées" />
               </HBox>

               <TableView fx:id="paymentsTable" prefHeight="350.0" style="-fx-background-radius: 12; -fx-border-color: #e5e7eb; -fx-border-radius: 12;" VBox.vgrow="ALWAYS">
                  <columns>
                     <TableColumn fx:id="colSelect" prefWidth="40.0" text="☑" />
                     <TableColumn fx:id="colId" prefWidth="60.0" text="ID" />
                     <TableColumn fx:id="colClient" prefWidth="140.0" text="Client" />
                     <TableColumn fx:id="colVehicle" prefWidth="120.0" text="Véhicule" />
                     <TableColumn fx:id="colAmount" prefWidth="90.0" text="Montant" />
                     <TableColumn fx:id="colMethod" prefWidth="80.0" text="Méthode" />
                     <TableColumn fx:id="colDate" prefWidth="90.0" text="Date" />
                     <TableColumn fx:id="colStatus" prefWidth="80.0" text="Statut" />
                     <TableColumn fx:id="colDueDate" prefWidth="90.0" text="Échéance" />
                     <TableColumn fx:id="colReference" prefWidth="100.0" text="Référence" />
                     <TableColumn fx:id="colActions" prefWidth="140.0" text="Actions" />
                  </columns>
               </TableView>
            </VBox>
         </VBox>

         <!-- Payment Details Sidebar -->
         <VBox spacing="15" style="-fx-background-color: #f8fafc; -fx-padding: 20; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 0 1;">
            <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="💳 Détails du Paiement" />

            <!-- Payment Detail Card -->
            <VBox fx:id="paymentDetailCard" spacing="12" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;">
               <Label fx:id="lblSelectedPayment" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Sélectionnez un paiement" />
               <VBox fx:id="paymentDetails" spacing="8" />
            </VBox>

            <!-- Quick Actions -->
            <VBox spacing="10">
               <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="⚡ Actions Rapides" />
               <VBox spacing="8">
                  <Button fx:id="btnMarkPaid" maxWidth="Infinity" onAction="#handleMarkPaid" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="✅ Marquer Payé" />
                  <Button fx:id="btnSendReminder" maxWidth="Infinity" onAction="#handleSendReminder" style="-fx-background-color: #f59e0b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="📧 Envoyer Rappel" />
                  <Button fx:id="btnViewContract" maxWidth="Infinity" onAction="#handleViewContract" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="📋 Voir Contrat" />
                  <Button fx:id="btnGenerateInvoice" maxWidth="Infinity" onAction="#handleGenerateInvoice" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 12px;" text="🧾 Générer Facture" />
               </VBox>
            </VBox>

            <!-- Payment Timeline -->
            <VBox spacing="10">
               <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📅 Chronologie" />
               <ScrollPane prefHeight="200" style="-fx-background-color: white; -fx-background-radius: 8; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-border-width: 1;">
                  <VBox fx:id="paymentTimeline" spacing="8" style="-fx-padding: 12;" />
               </ScrollPane>
            </VBox>
         </VBox>
      </SplitPane>
   </center>
   
   <bottom>
      <VBox style="-fx-background-color: #f8fafc; -fx-border-color: #e5e7eb; -fx-border-width: 1 0 0 0;">
         <!-- Statistics Summary -->
         <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-padding: 12 20;">
            <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #6b7280; -fx-font-weight: bold;" text="Total: 0 paiements" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblSelectedCount" style="-fx-font-size: 12px; -fx-text-fill: #3b82f6;" text="Sélectionnés: 0" />
            <Separator orientation="VERTICAL" />
            <Label fx:id="lblFilteredRevenue" style="-fx-font-size: 12px; -fx-text-fill: #059669; -fx-font-weight: bold;" text="Revenus filtrés: 0.00 DH" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblLastUpdate" style="-fx-font-size: 11px; -fx-text-fill: #9ca3af;" text="Dernière mise à jour: --:--" />
         </HBox>
         
         <!-- Action Buttons -->
         <HBox alignment="CENTER_RIGHT" spacing="12" style="-fx-padding: 12 20;">
            <Button fx:id="btnReconcile" onAction="#handleReconcile" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="🔄 Rapprochement" />
            <Button fx:id="btnAnalytics" onAction="#handleAnalytics" style="-fx-background-color: #8b5cf6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="📊 Analyses" />
            <Region HBox.hgrow="ALWAYS" />
            <Button onAction="#closeWindow" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="❌ Fermer" />
         </HBox>
      </VBox>
   </bottom>
</BorderPane>

<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox prefHeight="2758.0" prefWidth="1074.0" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueLocationsEnhancedController">
   <children>
      <!-- Scrollable Content -->
      <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;" VBox.vgrow="ALWAYS">
         <content>
            <VBox>
               <!-- Header -->
               <VBox style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 25;">
                  <HBox alignment="CENTER_LEFT" spacing="20">
                     <Label style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: bold;" text="🚗 Historique Détaillé des Locations" />
                     <Region HBox.hgrow="ALWAYS" />
                     <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12 20; -fx-font-weight: bold;" text="🔄 Actualiser" />
                  </HBox>

                  <!-- Filters -->
                  <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 15 0 0 0;">
                     <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="🔍 Filtres:" />
                     <ComboBox fx:id="filterStatusCombo" promptText="Statut" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 120;" />
                     <ComboBox fx:id="filterVehicleCombo" promptText="Véhicule" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 150;" />
                     <ComboBox fx:id="filterClientCombo" promptText="Client" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 150;" />
                     <DatePicker fx:id="filterDateFrom" promptText="Date début" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
                     <DatePicker fx:id="filterDateTo" promptText="Date fin" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6;" />
                     <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: rgba(255,255,255,0.3); -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="✖ Effacer" />
                  </HBox>

                  <!-- Advanced Filters -->
                  <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 10 0 0 0;">
                     <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px; -fx-font-weight: bold;" text="⚙️ Avancés:" />
                     <TextField fx:id="filterPriceMin" promptText="Prix min" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-max-width: 100;" />
                     <TextField fx:id="filterPriceMax" promptText="Prix max" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-max-width: 100;" />
                     <ComboBox fx:id="filterDurationCombo" promptText="Durée" style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 120;" />
                     <CheckBox fx:id="filterOverdueOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="En retard uniquement" />
                     <CheckBox fx:id="filterProfitableOnly" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 12px;" text="Rentables uniquement" />
                     <TextField fx:id="searchField" promptText="🔍 Rechercher..." style="-fx-background-color: rgba(255,255,255,0.9); -fx-background-radius: 6; -fx-min-width: 150;" />
                  </HBox>
               </VBox>

               <!-- Statistics Cards -->
               <HBox spacing="15" style="-fx-padding: 20;">
                  <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #e0f2fe; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #0288d1; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                     <Label fx:id="lblTotalLocations" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #01579b;" text="0" />
                     <Label style="-fx-font-size: 12px; -fx-text-fill: #0277bd; -fx-font-weight: bold;" text="📊 TOTAL LOCATIONS" />
                  </VBox>

                  <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #e8f5e8; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #4caf50; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                     <Label fx:id="lblActiveLocations" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #2e7d32;" text="0" />
                     <Label style="-fx-font-size: 12px; -fx-text-fill: #388e3c; -fx-font-weight: bold;" text="🟢 EN COURS" />
                  </VBox>

                  <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #f3e5f5; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #9c27b0; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                     <Label fx:id="lblCompletedLocations" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #7b1fa2;" text="0" />
                     <Label style="-fx-font-size: 12px; -fx-text-fill: #8e24aa; -fx-font-weight: bold;" text="✅ TERMINÉES" />
                  </VBox>

                  <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #ffebee; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #f44336; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                     <Label fx:id="lblOverdueLocations" style="-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #c62828;" text="0" />
                     <Label style="-fx-font-size: 12px; -fx-text-fill: #d32f2f; -fx-font-weight: bold;" text="⚠️ EN RETARD" />
                  </VBox>
               </HBox>

               <!-- Revenue and Analytics -->
               <HBox spacing="15" style="-fx-padding: 0 20 20 20;">
                  <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #fff3e0; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #ff9800; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                     <Label fx:id="lblTotalRevenue" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #e65100;" text="0.00 DH" />
                     <Label style="-fx-font-size: 12px; -fx-text-fill: #ef6c00; -fx-font-weight: bold;" text="💰 REVENUS TOTAL" />
                  </VBox>

                  <VBox alignment="CENTER" spacing="8" style="-fx-background-color: #fce4ec; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e91e63; -fx-border-radius: 12; -fx-border-width: 2;" HBox.hgrow="ALWAYS">
                     <Label fx:id="lblAverageRevenue" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #ad1457;" text="0.00 DH" />
                     <Label style="-fx-font-size: 12px; -fx-text-fill: #c2185b; -fx-font-weight: bold;" text="📈 REVENUS MOYEN" />
                  </VBox>
               </HBox>

               <!-- Charts -->
               <HBox spacing="15" style="-fx-padding: 0 20 20 20;">
                  <VBox spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="🚗 Utilisation des Véhicules" />
                     <VBox fx:id="vehicleUsageChart" spacing="8" />
                  </VBox>

                  <VBox spacing="10" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;" HBox.hgrow="ALWAYS">
                     <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📊 Revenus Mensuels" />
                     <VBox fx:id="revenueChart" spacing="8" />
                  </VBox>
               </HBox>

               <!-- Table -->
               <VBox spacing="12" style="-fx-padding: 0 20 20 20;">
                  <HBox alignment="CENTER_LEFT" spacing="15">
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📋 Historique des Locations" />
                     <Region HBox.hgrow="ALWAYS" />
                     <Button fx:id="btnBulkActions" onAction="#handleBulkActions" style="-fx-background-color: #667eea; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16;" text="⚡ Actions Groupées" />
                  </HBox>

                  <TableView fx:id="locationsTable" prefHeight="1583.0" prefWidth="1034.0" style="-fx-background-radius: 12; -fx-border-color: #e5e7eb; -fx-border-radius: 12;" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="colSelect" prefWidth="40.0" text="☑" />
                        <TableColumn fx:id="colId" prefWidth="60.0" text="ID" />
                        <TableColumn fx:id="colClient" prefWidth="140.0" text="Client" />
                        <TableColumn fx:id="colVehicle" prefWidth="120.0" text="Véhicule" />
                        <TableColumn fx:id="colDateDebut" prefWidth="90.0" text="Début" />
                        <TableColumn fx:id="colDateFin" prefWidth="90.0" text="Fin" />
                        <TableColumn fx:id="colStatus" prefWidth="80.0" text="Statut" />
                        <TableColumn fx:id="colPrice" prefWidth="90.0" text="Prix" />
                        <TableColumn fx:id="colDuration" prefWidth="80.0" text="Durée" />
                        <TableColumn fx:id="colProfitability" prefWidth="100.0" text="Rentabilité" />
                        <TableColumn fx:id="colActions" prefWidth="140.0" text="Actions" />
                     </columns>
                  </TableView>
               </VBox>

               <!-- Sidebar (simplified) -->
               <VBox spacing="15" style="-fx-background-color: #f8fafc; -fx-padding: 20; -fx-border-color: #e2e8f0; -fx-border-width: 1 0 0 0;">
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📄 Détails de la Location" />

                  <VBox fx:id="locationDetailCard" spacing="12" style="-fx-background-color: white; -fx-background-radius: 12; -fx-padding: 20; -fx-border-color: #e5e7eb; -fx-border-radius: 12; -fx-border-width: 1;">
                     <Label fx:id="lblSelectedLocation" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="Sélectionnez une location" />
                     <VBox fx:id="locationDetails" spacing="8" />
                  </VBox>

                  <VBox spacing="10">
                     <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #1f2937;" text="📈 Chronologie" />
                     <ScrollPane prefHeight="150" style="-fx-background-color: white; -fx-background-radius: 8; -fx-border-color: #e5e7eb; -fx-border-radius: 8; -fx-border-width: 1;">
                        <VBox fx:id="locationTimeline" spacing="8" style="-fx-padding: 12;" />
                     </ScrollPane>
                  </VBox>
               </VBox>
            </VBox>
         </content>
      </ScrollPane>

      <!-- Bottom Status (outside scroll pane) -->
      <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-background-color: #f8fafc; -fx-border-color: #e5e7eb; -fx-border-width: 1 0 0 0; -fx-padding: 12 20;">
         <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #6b7280; -fx-font-weight: bold;" text="Total: 0 locations" />
         <Separator orientation="VERTICAL" />
         <Label fx:id="lblSelectedCount" style="-fx-font-size: 12px; -fx-text-fill: #3b82f6;" text="Sélectionnés: 0" />
         <Separator orientation="VERTICAL" />
         <Label fx:id="lblFilteredRevenue" style="-fx-font-size: 12px; -fx-text-fill: #059669; -fx-font-weight: bold;" text="Revenus filtrés: 0.00 DH" />
         <Region HBox.hgrow="ALWAYS" />
         <Label fx:id="lblLastUpdate" style="-fx-font-size: 11px; -fx-text-fill: #9ca3af;" text="Dernière mise à jour: --:--" />
         <Button onAction="#closeWindow" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 12px;" text="❌ Fermer" />
      </HBox>
   </children>
</VBox>

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS locationdb DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE locationdb;

-- Table Admin
CREATE TABLE IF NOT EXISTS admin (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) NOT NULL UNIQUE,
    passwordHash VARCHAR(255) NOT NULL
);

-- Table Client
CREATE TABLE IF NOT EXISTS client (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    cin VARCHAR(20) NOT NULL UNIQUE,
    telephone VARCHAR(20),
    email VARCHAR(100)
);

-- Table Vehicule
CREATE TABLE IF NOT EXISTS vehicule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    marque VARCHAR(100) NOT NULL,
    modele VARCHAR(100) NOT NULL,
    immatriculation VARCHAR(50) NOT NULL UNIQUE,
    etat VARCHAR(20) NOT NULL,
    prix<PERSON>arJour DOUBLE NOT NULL,
    photoUrl VARCHAR(255),
    carburant VARCHAR(20) NOT NULL,
    metrage INT NOT NULL,
    dateAcquisition DATE NOT NULL,
    lastUsed DATE,
    nbreChevaux INT,
    assuranceCompagnie VARCHAR(100),
    assuranceExpiration DATE,
    assuranceNumero VARCHAR(50)
);

-- Table Location
CREATE TABLE IF NOT EXISTS location (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    client_id BIGINT,
    vehicule_id BIGINT,
    dateDebut DATE,
    dateFinPrevue DATE,
    dateFinReelle DATE,
    prixTotal DOUBLE,
    penalite DOUBLE,
    FOREIGN KEY (client_id) REFERENCES client(id),
    FOREIGN KEY (vehicule_id) REFERENCES vehicule(id)
);

-- Table Paiement
CREATE TABLE IF NOT EXISTS paiement (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    location_id BIGINT,
    montant DOUBLE,
    datePaiement DATE,
    FOREIGN KEY (location_id) REFERENCES location(id)
);

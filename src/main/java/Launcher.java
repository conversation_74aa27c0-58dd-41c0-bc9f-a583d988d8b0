/**
 * Launcher class for the JavaFX application.
 * This class is needed to avoid module path issues when creating native executables.
 * It simply launches the main JavaFX application.
 */
import javafx.application.Application;
import util.MemoryManager;
import util.AsyncTaskManager;
import util.CacheManager;
import util.HibernateUtil;

public class Launcher {
    
    public static void main(String[] args) {
        // Print optimization suggestions
        MemoryManager.printOptimizationSuggestions();
        
        // Start memory management
        MemoryManager.startMemoryManagement();
        
        // Initialize cache manager
        CacheManager.getInstance();
        
        // Add shutdown hook for proper cleanup
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("Application shutting down...");
            
            // Cleanup resources
            AsyncTaskManager.getInstance().shutdown();
            MemoryManager.stopMemoryManagement();
            CacheManager.getInstance().clearAll();
            HibernateUtil.shutdown();
            
            System.out.println("Cleanup completed");
        }));
        
        try {
            // Initialize database before launching JavaFX
            util.DatabaseInitializer.initialize();
            
            // Launch the JavaFX application
            Application.launch(MainApp.class, args);
        } catch (Exception e) {
            System.err.println("Failed to launch application: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}

package dao;

import model.VehicleFailure;
import model.Vehicule;
import java.time.LocalDate;
import java.util.List;

public class VehicleFailureDAO extends BaseDAO<VehicleFailure, Long> {

    public VehicleFailureDAO() {
        super(VehicleFailure.class);
    }

    @Override
    public void save(VehicleFailure failure) {
        failure.setUpdatedDate(LocalDate.now());
        super.save(failure);
    }

    @Override
    public VehicleFailure findById(Long id) {
        return executeQuerySingle("SELECT f FROM VehicleFailure f " +
                                  "LEFT JOIN FETCH f.vehicule " +
                                  "WHERE f.id = :id", "id", id);
    }

    @Override
    public List<VehicleFailure> findAll() {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "LEFT JOIN FETCH f.vehicule " +
                           "ORDER BY f.failureDate DESC");
    }
    
    public List<VehicleFailure> findByVehicle(Vehicule vehicule) {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "WHERE f.vehicule = :vehicule " +
                           "ORDER BY f.failureDate DESC", "vehicule", vehicule);
    }

    public List<VehicleFailure> findUnresolvedFailures() {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "LEFT JOIN FETCH f.vehicule " +
                           "WHERE f.status != :repaired " +
                           "ORDER BY f.severity DESC, f.failureDate ASC",
                           "repaired", VehicleFailure.RepairStatus.REPARE);
    }

    public List<VehicleFailure> findCriticalFailures() {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "LEFT JOIN FETCH f.vehicule " +
                           "WHERE f.severity = :critical " +
                           "AND f.status != :repaired " +
                           "ORDER BY f.failureDate ASC",
                           "critical", VehicleFailure.FailureSeverity.CRITIQUE,
                           "repaired", VehicleFailure.RepairStatus.REPARE);
    }

    public List<VehicleFailure> findByType(VehicleFailure.FailureType type) {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "LEFT JOIN FETCH f.vehicule " +
                           "WHERE f.type = :type " +
                           "ORDER BY f.failureDate DESC", "type", type);
    }

    public List<VehicleFailure> findBySeverity(VehicleFailure.FailureSeverity severity) {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "LEFT JOIN FETCH f.vehicule " +
                           "WHERE f.severity = :severity " +
                           "ORDER BY f.failureDate DESC", "severity", severity);
    }

    public List<VehicleFailure> findByStatus(VehicleFailure.RepairStatus status) {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "LEFT JOIN FETCH f.vehicule " +
                           "WHERE f.status = :status " +
                           "ORDER BY f.failureDate DESC", "status", status);
    }
    
    public List<VehicleFailure> findByDateRange(LocalDate startDate, LocalDate endDate) {
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "LEFT JOIN FETCH f.vehicule " +
                           "WHERE f.failureDate BETWEEN :startDate AND :endDate " +
                           "ORDER BY f.failureDate DESC",
                           "startDate", startDate, "endDate", endDate);
    }

    public List<VehicleFailure> findRecentFailuresForVehicle(Vehicule vehicule, int days) {
        LocalDate cutoffDate = LocalDate.now().minusDays(days);
        return executeQuery("SELECT f FROM VehicleFailure f " +
                           "WHERE f.vehicule = :vehicule " +
                           "AND f.failureDate >= :cutoffDate " +
                           "ORDER BY f.failureDate DESC",
                           "vehicule", vehicule, "cutoffDate", cutoffDate);
    }
    
    public long getFailureCountByVehicle(Vehicule vehicule) {
        Long result = executeScalarQuery("SELECT COUNT(f) FROM VehicleFailure f WHERE f.vehicule = :vehicule",
                                        Long.class, "vehicule", vehicule);
        return result != null ? result : 0L;
    }

    public long getFailureCountByType(VehicleFailure.FailureType type) {
        Long result = executeScalarQuery("SELECT COUNT(f) FROM VehicleFailure f WHERE f.type = :type",
                                        Long.class, "type", type);
        return result != null ? result : 0L;
    }

    public Double getTotalRepairCostForVehicle(Vehicule vehicule) {
        Double result = executeScalarQuery("SELECT SUM(f.repairCost) FROM VehicleFailure f " +
                                          "WHERE f.vehicule = :vehicule " +
                                          "AND f.repairCost IS NOT NULL",
                                          Double.class, "vehicule", vehicule);
        return result != null ? result : 0.0;
    }

    public Double getTotalRepairCostByDateRange(LocalDate startDate, LocalDate endDate) {
        Double result = executeScalarQuery("SELECT SUM(f.repairCost) FROM VehicleFailure f " +
                                          "WHERE f.repairDate BETWEEN :startDate AND :endDate " +
                                          "AND f.repairCost IS NOT NULL",
                                          Double.class, "startDate", startDate, "endDate", endDate);
        return result != null ? result : 0.0;
    }
    
    public Double getAverageRepairTimeByType(VehicleFailure.FailureType type) {
        org.hibernate.Session session = util.HibernateUtil.getSessionFactory().openSession();
        try {
            @SuppressWarnings("unchecked")
            List<Object[]> results = session.createQuery(
                "SELECT f.failureDate, f.repairDate FROM VehicleFailure f " +
                "WHERE f.type = :type " +
                "AND f.failureDate IS NOT NULL " +
                "AND f.repairDate IS NOT NULL"
            ).setParameter("type", type).list();

            if (results.isEmpty()) return 0.0;

            long totalDays = 0;
            for (Object[] result : results) {
                LocalDate failureDate = (LocalDate) result[0];
                LocalDate repairDate = (LocalDate) result[1];
                totalDays += java.time.temporal.ChronoUnit.DAYS.between(failureDate, repairDate);
            }

            return (double) totalDays / results.size();
        } finally {
            session.close();
        }
    }
    
    public List<VehicleFailure> findRecurringFailures(VehicleFailure.FailureType type, int occurrences) {
        org.hibernate.Session session = util.HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery(
                "SELECT f FROM VehicleFailure f " +
                "LEFT JOIN FETCH f.vehicule " +
                "WHERE f.vehicule IN (" +
                "  SELECT f2.vehicule FROM VehicleFailure f2 " +
                "  WHERE f2.type = :type " +
                "  GROUP BY f2.vehicule " +
                "  HAVING COUNT(f2) >= :occurrences" +
                ") AND f.type = :type " +
                "ORDER BY f.vehicule.id, f.failureDate DESC",
                VehicleFailure.class
            ).setParameter("type", type)
             .setParameter("occurrences", (long) occurrences)
             .list();
        } finally {
            session.close();
        }
    }

    // delete() method is inherited from BaseDAO

    public void deleteById(Long id) {
        VehicleFailure failure = findById(id);
        if (failure != null) {
            delete(failure);
        }
    }
}

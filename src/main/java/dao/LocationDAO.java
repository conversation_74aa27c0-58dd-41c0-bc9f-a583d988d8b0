package dao;

import model.Location;
import org.hibernate.Session;
import util.HibernateUtil;

import java.util.List;

public class LocationDAO extends BaseDAO<Location, Long> {

    public LocationDAO() {
        super(Location.class);
    }

    public List<Location> findByVehiculeId(Long vehiculeId) {
        return executeQuery("from Location where vehicule.id = :vehiculeId order by dateDebut desc", "vehiculeId", vehiculeId);
    }

    public List<Location> findByClientId(Long clientId) {
        return executeQuery("from Location where client.id = :clientId order by dateDebut desc", "clientId", clientId);
    }

    public List<Location> findActiveByVehiculeId(Long vehiculeId) {
        return executeQuery("from Location where vehicule.id = :vehiculeId and (dateFinReelle is null or dateFinReelle > current_date) order by dateDebut desc",
                           "vehiculeId", vehiculeId);
    }

    /**
     * Checks if a vehicle is available for the given period (no overlap with existing locations).
     * If excludeLocationId is not null, excludes that location (for update scenarios).
     */
    public boolean isVehiculeAvailable(Long vehiculeId, java.time.LocalDate start, java.time.LocalDate end, Long excludeLocationId) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "from Location where vehicule.id = :vehiculeId " +
                    "and ((dateDebut <= :end and dateFinPrevue >= :start)" +
                    (excludeLocationId != null ? " and id != :excludeId" : "") + ")";
            var query = session.createQuery(hql, Location.class)
                    .setParameter("vehiculeId", vehiculeId)
                    .setParameter("start", start)
                    .setParameter("end", end);
            if (excludeLocationId != null) {
                query.setParameter("excludeId", excludeLocationId);
            }
            return query.list().isEmpty();
        } finally {
            session.close();
        }
    }

    /**
     * Batch update status for all locations (optional utility).
     */
    public void updateAllStatuses() {
        List<Location> all = findAll();
        for (Location loc : all) {
            loc.updateStatus();
            save(loc);
        }
    }
}

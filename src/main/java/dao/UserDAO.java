package dao;

import model.Admin;
import model.Agent;
import model.User;
import java.util.List;

public class UserDAO extends BaseDAO<User, Long> {

    private final AdminDAO adminDAO = new AdminDAO();
    private final AgentDAO agentDAO = new AgentDAO();

    public UserDAO() {
        super(User.class);
    }

    // Unified user lookup for login
    public User findUserByUsername(String username) {
        Admin admin = adminDAO.findByUsername(username);
        if (admin != null) return admin;
        Agent agent = agentDAO.findByUsername(username);
        if (agent != null) return agent;
        return null;
    }

    public List<Admin> findAllAdmins() {
        return adminDAO.findAll();
    }

    public List<Agent> findAllAgents() {
        return agentDAO.findAll();
    }

    public Admin findAdminById(Long id) {
        return adminDAO.findById(id);
    }

    public Agent findAgentById(Long id) {
        return agentDAO.findById(id);
    }

    public Admin findAdminByUsername(String username) {
        return adminDAO.findByUsername(username);
    }

    public Agent findAgentByUsername(String username) {
        return agentDAO.findByUsername(username);
    }

    @Override
    public List<User> findAll() {
        List<User> allUsers = new java.util.ArrayList<>();
        allUsers.addAll(findAllAdmins());
        allUsers.addAll(findAllAgents());
        return allUsers;
    }
}

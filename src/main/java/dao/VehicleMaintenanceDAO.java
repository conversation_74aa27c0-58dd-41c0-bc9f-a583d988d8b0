package dao;

import model.VehicleMaintenance;
import model.Vehicule;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.time.LocalDate;
import java.util.List;

public class VehicleMaintenanceDAO extends BaseDAO<VehicleMaintenance, Long> {

    public VehicleMaintenanceDAO() {
        super(VehicleMaintenance.class);
    }

    @Override
    public void save(VehicleMaintenance maintenance) {
        maintenance.setUpdatedDate(LocalDate.now());
        super.save(maintenance);
    }

    @Override
    public VehicleMaintenance findById(Long id) {
        return executeQuerySingle("SELECT m FROM VehicleMaintenance m " +
                                  "LEFT JOIN FETCH m.vehicule " +
                                  "WHERE m.id = :id", "id", id);
    }

    @Override
    public List<VehicleMaintenance> findAll() {
        return executeQuery("SELECT m FROM VehicleMaintenance m " +
                           "LEFT JOIN FETCH m.vehicule " +
                           "ORDER BY m.nextMaintenanceDate ASC");
    }
    
    public List<VehicleMaintenance> findByVehicle(Vehicule vehicule) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "WHERE m.vehicule = :vehicule " +
                "ORDER BY m.maintenanceDate DESC", 
                VehicleMaintenance.class
            ).setParameter("vehicule", vehicule).list();
        }
    }
    
    public List<VehicleMaintenance> findOverdueMaintenance() {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.nextMaintenanceDate < :today " +
                "AND m.status != :completed " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).setParameter("today", LocalDate.now())
             .setParameter("completed", VehicleMaintenance.MaintenanceStatus.TERMINE)
             .list();
        }
    }
    
    public List<VehicleMaintenance> findUpcomingMaintenance(int days) {
        LocalDate futureDate = LocalDate.now().plusDays(days);
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.nextMaintenanceDate BETWEEN :today AND :futureDate " +
                "AND m.status != :completed " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).setParameter("today", LocalDate.now())
             .setParameter("futureDate", futureDate)
             .setParameter("completed", VehicleMaintenance.MaintenanceStatus.TERMINE)
             .list();
        }
    }
    
    public List<VehicleMaintenance> findByType(VehicleMaintenance.MaintenanceType type) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.type = :type " +
                "ORDER BY m.maintenanceDate DESC", 
                VehicleMaintenance.class
            ).setParameter("type", type).list();
        }
    }
    
    public List<VehicleMaintenance> findByStatus(VehicleMaintenance.MaintenanceStatus status) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT m FROM VehicleMaintenance m " +
                "LEFT JOIN FETCH m.vehicule " +
                "WHERE m.status = :status " +
                "ORDER BY m.nextMaintenanceDate ASC", 
                VehicleMaintenance.class
            ).setParameter("status", status).list();
        }
    }

    public VehicleMaintenance findLastMaintenanceForVehicle(Vehicule vehicule) {
        return executeQuerySingle("SELECT m FROM VehicleMaintenance m " +
                                  "WHERE m.vehicule = :vehicule " +
                                  "AND m.status = :completed " +
                                  "ORDER BY m.maintenanceDate DESC",
                                  "vehicule", vehicule,
                                  "completed", VehicleMaintenance.MaintenanceStatus.TERMINE);
    }
    
    public VehicleMaintenance findNextMaintenanceForVehicle(Vehicule vehicule) {
        return executeQuerySingle("SELECT m FROM VehicleMaintenance m " +
                                  "WHERE m.vehicule = :vehicule " +
                                  "AND m.nextMaintenanceDate >= :today " +
                                  "AND m.status != :completed " +
                                  "ORDER BY m.nextMaintenanceDate ASC",
                                  "vehicule", vehicule,
                                  "today", LocalDate.now(),
                                  "completed", VehicleMaintenance.MaintenanceStatus.TERMINE);
    }

    public Double getTotalMaintenanceCostForVehicle(Vehicule vehicule) {
        Double result = executeScalarQuery("SELECT SUM(m.cost) FROM VehicleMaintenance m " +
                                          "WHERE m.vehicule = :vehicule " +
                                          "AND m.cost IS NOT NULL", Double.class, "vehicule", vehicule);
        return result != null ? result : 0.0;
    }

    public Double getTotalMaintenanceCostByDateRange(LocalDate startDate, LocalDate endDate) {
        Double result = executeScalarQuery("SELECT SUM(m.cost) FROM VehicleMaintenance m " +
                                          "WHERE m.maintenanceDate BETWEEN :startDate AND :endDate " +
                                          "AND m.cost IS NOT NULL", Double.class,
                                          "startDate", startDate, "endDate", endDate);
        return result != null ? result : 0.0;
    }
    
    public long getMaintenanceCountByType(VehicleMaintenance.MaintenanceType type) {
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            return session.createQuery(
                "SELECT COUNT(m) FROM VehicleMaintenance m WHERE m.type = :type", 
                Long.class
            ).setParameter("type", type).uniqueResult();
        }
    }
    
    public void delete(VehicleMaintenance maintenance) {
        Transaction transaction = null;
        try (Session session = HibernateUtil.getSessionFactory().openSession()) {
            transaction = session.beginTransaction();
            session.delete(maintenance);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            throw new RuntimeException("Error deleting maintenance: " + e.getMessage(), e);
        }
    }
    
    public void deleteById(Long id) {
        VehicleMaintenance maintenance = findById(id);
        if (maintenance != null) {
            delete(maintenance);
        }
    }
}

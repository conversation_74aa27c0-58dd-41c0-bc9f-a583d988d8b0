package dao;

import model.NotificationSettings;
import model.User;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import util.HibernateUtil;
import java.util.List;

public class NotificationSettingsDAO extends BaseDAO<NotificationSettings, Long> {

    public NotificationSettingsDAO() {
        super(NotificationSettings.class);
    }

    public NotificationSettings findByUser(User user) {
        return executeQuerySingle("from NotificationSettings where user = :user", "user", user);
    }

    public NotificationSettings findOrCreateByUser(User user) {
        NotificationSettings settings = findByUser(user);
        if (settings == null) {
            settings = new NotificationSettings(user);
            save(settings);
        }
        return settings;
    }

    // findAll() and delete() are inherited from BaseDAO

    public void resetToDefaults(User user) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            // Delete existing settings
            Query<?> deleteQuery = session.createQuery("delete from NotificationSettings where user = :user");
            deleteQuery.setParameter("user", user);
            deleteQuery.executeUpdate();
            
            // Create new default settings
            NotificationSettings defaultSettings = new NotificationSettings(user);
            session.persist(defaultSettings);
            
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<User> findUsersWithDesktopNotificationsEnabled() {
        return executeListQuery("select ns.user from NotificationSettings ns where ns.desktopNotificationsEnabled = true",
                                User.class);
    }

    public List<User> findUsersWithEmailNotificationsEnabled() {
        return executeListQuery("select ns.user from NotificationSettings ns where ns.emailNotificationsEnabled = true",
                                User.class);
    }
}

package dao;

import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import util.HibernateUtil;
import javafx.concurrent.Task;
import javafx.application.Platform;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

/**
 * Optimized Base DAO class with async operations and caching
 * @param <T> Entity type
 * @param <ID> ID type
 */
public abstract class BaseDAO<T, ID> {
    
    private final Class<T> entityClass;
    private static final ExecutorService executorService = Executors.newFixedThreadPool(4);
    private static final ConcurrentHashMap<String, Object> cache = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRY = 300000; // 5 minutes
    private static final ConcurrentHashMap<String, Long> cacheTimestamps = new ConcurrentHashMap<>();
    
    protected BaseDAO(Class<T> entityClass) {
        this.entityClass = entityClass;
    }
    
    /**
     * Async save operation to prevent UI blocking
     */
    public CompletableFuture<Void> saveAsync(T entity) {
        return CompletableFuture.runAsync(() -> {
            Session session = null;
            Transaction tx = null;
            try {
                session = HibernateUtil.getSessionFactory().openSession();
                tx = session.beginTransaction();
                session.merge(entity);
                tx.commit();
                
                // Invalidate cache for this entity type
                invalidateCache(entityClass.getSimpleName());
            } catch (Exception e) {
                if (tx != null && tx.isActive()) tx.rollback();
                throw new RuntimeException("Error saving entity", e);
            } finally {
                if (session != null) session.close();
            }
        }, executorService);
    }
    
    /**
     * Synchronous save for when immediate result is needed
     */
    public void save(T entity) {
        Session session = null;
        Transaction tx = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            tx = session.beginTransaction();
            session.merge(entity);
            tx.commit();
            
            // Invalidate cache
            invalidateCache(entityClass.getSimpleName());
        } catch (Exception e) {
            if (tx != null && tx.isActive()) tx.rollback();
            throw new RuntimeException("Error saving entity", e);
        } finally {
            if (session != null) session.close();
        }
    }
    
    /**
     * Async delete operation
     */
    public CompletableFuture<Void> deleteAsync(T entity) {
        return CompletableFuture.runAsync(() -> {
            Session session = null;
            Transaction tx = null;
            try {
                session = HibernateUtil.getSessionFactory().openSession();
                tx = session.beginTransaction();
                session.remove(session.merge(entity));
                tx.commit();
                
                invalidateCache(entityClass.getSimpleName());
            } catch (Exception e) {
                if (tx != null && tx.isActive()) tx.rollback();
                throw new RuntimeException("Error deleting entity", e);
            } finally {
                if (session != null) session.close();
            }
        }, executorService);
    }
    
    /**
     * Cached findAll with async loading
     */
    @SuppressWarnings("unchecked")
    public CompletableFuture<List<T>> findAllAsync() {
        String cacheKey = entityClass.getSimpleName() + "_all";
        
        // Check cache first
        if (isCacheValid(cacheKey)) {
            List<T> cachedResult = (List<T>) cache.get(cacheKey);
            if (cachedResult != null) {
                return CompletableFuture.completedFuture(cachedResult);
            }
        }
        
        return CompletableFuture.supplyAsync(() -> {
            Session session = null;
            try {
                session = HibernateUtil.getSessionFactory().openSession();
                Query<T> query = session.createQuery("from " + entityClass.getSimpleName(), entityClass);
                
                // Use pagination for large datasets
                query.setMaxResults(1000); // Limit to prevent memory issues
                
                List<T> result = query.list();
                
                // Cache the result
                cache.put(cacheKey, result);
                cacheTimestamps.put(cacheKey, System.currentTimeMillis());
                
                return result;
            } finally {
                if (session != null) session.close();
            }
        }, executorService);
    }
    
    /**
     * Synchronous findAll with caching
     */
    @SuppressWarnings("unchecked")
    public List<T> findAll() {
        String cacheKey = entityClass.getSimpleName() + "_all";
        
        // Check cache first
        if (isCacheValid(cacheKey)) {
            List<T> cachedResult = (List<T>) cache.get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }
        }
        
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<T> query = session.createQuery("from " + entityClass.getSimpleName(), entityClass);
            query.setMaxResults(1000); // Prevent memory issues
            
            List<T> result = query.list();
            
            // Cache the result
            cache.put(cacheKey, result);
            cacheTimestamps.put(cacheKey, System.currentTimeMillis());
            
            return result;
        } finally {
            if (session != null) session.close();
        }
    }
    
    /**
     * Paginated findAll for better performance
     */
    public List<T> findAll(int page, int pageSize) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<T> query = session.createQuery("from " + entityClass.getSimpleName(), entityClass);
            query.setFirstResult(page * pageSize);
            query.setMaxResults(pageSize);
            return query.list();
        } finally {
            if (session != null) session.close();
        }
    }
    
    /**
     * Cached findById
     */
    @SuppressWarnings("unchecked")
    public T findById(ID id) {
        String cacheKey = entityClass.getSimpleName() + "_" + id;
        
        if (isCacheValid(cacheKey)) {
            T cachedResult = (T) cache.get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }
        }
        
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            T result = session.get(entityClass, id);
            
            if (result != null) {
                cache.put(cacheKey, result);
                cacheTimestamps.put(cacheKey, System.currentTimeMillis());
            }
            
            return result;
        } finally {
            if (session != null) session.close();
        }
    }
    
    /**
     * Optimized query execution with caching
     */
    protected List<T> executeQuery(String hql, Object... parameters) {
        String cacheKey = generateCacheKey(hql, parameters);
        
        @SuppressWarnings("unchecked")
        List<T> cachedResult = (List<T>) cache.get(cacheKey);
        if (isCacheValid(cacheKey) && cachedResult != null) {
            return cachedResult;
        }
        
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Query<T> query = session.createQuery(hql, entityClass);
            
            // Set parameters efficiently
            for (int i = 0; i < parameters.length; i += 2) {
                query.setParameter((String) parameters[i], parameters[i + 1]);
            }
            
            List<T> result = query.list();
            
            // Cache the result
            cache.put(cacheKey, result);
            cacheTimestamps.put(cacheKey, System.currentTimeMillis());
            
            return result;
        } finally {
            if (session != null) session.close();
        }
    }
    
    /**
     * Batch operations for better performance
     */
    public void saveBatch(List<T> entities) {
        if (entities == null || entities.isEmpty()) return;
        
        Session session = null;
        Transaction tx = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            tx = session.beginTransaction();
            
            int batchSize = 50;
            for (int i = 0; i < entities.size(); i++) {
                session.merge(entities.get(i));
                
                if (i % batchSize == 0) {
                    session.flush();
                    session.clear();
                }
            }
            
            tx.commit();
            invalidateCache(entityClass.getSimpleName());
        } catch (Exception e) {
            if (tx != null && tx.isActive()) tx.rollback();
            throw new RuntimeException("Error in batch save", e);
        } finally {
            if (session != null) session.close();
        }
    }
    
    /**
     * Count with caching
     */
    public long count() {
        String cacheKey = entityClass.getSimpleName() + "_count";
        
        if (isCacheValid(cacheKey)) {
            Long cachedResult = (Long) cache.get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }
        }
        
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            Long result = session.createQuery("select count(*) from " + entityClass.getSimpleName(), Long.class)
                    .uniqueResult();
            
            cache.put(cacheKey, result);
            cacheTimestamps.put(cacheKey, System.currentTimeMillis());
            
            return result != null ? result : 0L;
        } finally {
            if (session != null) session.close();
        }
    }
    
    // Cache management methods
    private boolean isCacheValid(String cacheKey) {
        Long timestamp = cacheTimestamps.get(cacheKey);
        return timestamp != null && (System.currentTimeMillis() - timestamp) < CACHE_EXPIRY;
    }
    
    private void invalidateCache(String entityType) {
        cache.entrySet().removeIf(entry -> entry.getKey().startsWith(entityType));
        cacheTimestamps.entrySet().removeIf(entry -> entry.getKey().startsWith(entityType));
    }
    
    private String generateCacheKey(String hql, Object... parameters) {
        StringBuilder key = new StringBuilder(hql);
        for (Object param : parameters) {
            key.append("_").append(param);
        }
        return key.toString();
    }
    
    /**
     * Clear all cache
     */
    public static void clearCache() {
        cache.clear();
        cacheTimestamps.clear();
    }
    
    /**
     * Shutdown executor service
     */
    public static void shutdown() {
        executorService.shutdown();
    }
}

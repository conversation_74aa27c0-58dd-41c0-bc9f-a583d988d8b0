package dao;

import model.Client;

public class ClientDAO extends BaseDAO<Client, Long> {

    public ClientDAO() {
        super(Client.class);
    }

    public Client findByCin(String cin) {
        return executeQuerySingle("from Client where cin = :cin", "cin", cin);
    }

    public Client findByEmail(String email) {
        return executeQuerySingle("from Client where email = :email", "email", email);
    }
}

package dao;

import model.Vehicule;
import java.util.List;

public class VehiculeDAO extends BaseDAO<Vehicule, Long> {

    public VehiculeDAO() {
        super(Vehicule.class);
    }

    public Vehicule findByImmatriculation(String immatriculation) {
        return executeQuerySingle("from Vehicule where immatriculation = :immatriculation", "immatriculation", immatriculation);
    }

    public List<Vehicule> findByEtat(String etat) {
        return executeQuery("from Vehicule where etat = :etat", "etat", etat);
    }

    public List<Vehicule> searchVehicules(String searchTerm) {
        return executeQuery("from Vehicule where lower(marque) like :search or lower(modele) like :search or lower(immatriculation) like :search",
                           "search", "%" + searchTerm.toLowerCase() + "%");
    }
}

package dao;

import model.Paiement;
import org.hibernate.Session;
import util.HibernateUtil;
import java.util.List;

public class PaiementDAO extends BaseDAO<Paiement, Long> {

    public PaiementDAO() {
        super(Paiement.class);
    }

    @Override
    public List<Paiement> findAll() {
        return executeQuery("SELECT p FROM Paiement p " +
                           "LEFT JOIN FETCH p.location l " +
                           "LEFT JOIN FETCH l.client " +
                           "LEFT JOIN FETCH l.vehicule " +
                           "ORDER BY p.datePaiement DESC");
    }

    @Override
    public Paiement findById(Long id) {
        return executeQuerySingle("SELECT p FROM Paiement p " +
                                  "LEFT JOIN FETCH p.location l " +
                                  "LEFT JOIN FETCH l.client " +
                                  "LEFT JOIN FETCH l.vehicule " +
                                  "WHERE p.id = :id", "id", id);
    }

    public List<Paiement> findByLocationId(Long locationId) {
        return executeQuery("from Paiement where location.id = :locationId order by datePaiement desc", "locationId", locationId);
    }

    public double getTotalPaiementsByLocation(Long locationId) {
        Double result = executeScalarQuery("select sum(montant) from Paiement where location.id = :locationId",
                                          Double.class, "locationId", locationId);
        return result != null ? result : 0.0;
    }

    public List<Paiement> findByDateRange(java.time.LocalDate startDate, java.time.LocalDate endDate) {
        return executeQuery("SELECT p FROM Paiement p " +
                           "LEFT JOIN FETCH p.location l " +
                           "LEFT JOIN FETCH l.client " +
                           "LEFT JOIN FETCH l.vehicule " +
                           "WHERE p.datePaiement BETWEEN :startDate AND :endDate " +
                           "ORDER BY p.datePaiement DESC",
                           "startDate", startDate, "endDate", endDate);
    }

    public double getTotalRevenue() {
        Double result = executeScalarQuery("select sum(montant) from Paiement", Double.class);
        return result != null ? result : 0.0;
    }
}

package controller;

import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.chart.*;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.geometry.Insets;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import dao.*;
import model.*;
import util.AsyncTaskManager;
import util.CacheManager;
import util.MemoryManager;

import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class DashboardContentController implements Initializable {

    // Statistics Labels
    @FXML private Label lblTotalVehicles;
    @FXML private Label lblAvailableVehicles;
    @FXML private Label lblRentedVehicles;
    @FXML private Label lblMaintenanceVehicles;
    @FXML private Label lblTotalClients;
    @FXML private Label lblActiveRentals;
    @FXML private Label lblTodayRevenue;
    @FXML private Label lblMonthRevenue;
    @FXML private Label lblPendingReturns;
    @FXML private Label lblOverdueReturns;

    // Charts
    @FXML private PieChart vehicleStatusChart;
    @FXML private LineChart<String, Number> revenueChart;
    @FXML private BarChart<String, Number> monthlyRentalsChart;
    @FXML private AreaChart<String, Number> clientGrowthChart;

    // Containers
    @FXML private VBox recentActivitiesContainer;
    @FXML private VBox upcomingReturnsContainer;
    @FXML private VBox alertsContainer;

    // DAOs
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private final ClientDAO clientDAO = new ClientDAO();
    private final LocationDAO locationDAO = new LocationDAO();
    private final PaiementDAO paiementDAO = new PaiementDAO();

    // Utilities
    private final AsyncTaskManager taskManager = AsyncTaskManager.getInstance();
    private final CacheManager cacheManager = CacheManager.getInstance();

    // Data refresh tracking
    private volatile boolean isRefreshing = false;
    private static final String DASHBOARD_CACHE = "dashboard_data";

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupCharts();
        loadDashboardDataAsync();
        
        // Schedule periodic refresh every 5 minutes
        taskManager.scheduleRecurring("dashboard_refresh", 
            this::refreshDashboardData, 
            5, 5, java.util.concurrent.TimeUnit.MINUTES);
    }

    /**
     * Setup chart configurations for better performance
     */
    private void setupCharts() {
        // Configure charts for better performance
        if (vehicleStatusChart != null) {
            vehicleStatusChart.setAnimated(false); // Disable animations for better performance
            vehicleStatusChart.setLegendVisible(true);
            vehicleStatusChart.setTitle("État des Véhicules");
        }

        if (revenueChart != null) {
            revenueChart.setAnimated(false);
            revenueChart.setTitle("Revenus des 30 derniers jours");
            revenueChart.getXAxis().setLabel("Date");
            revenueChart.getYAxis().setLabel("Revenus (DH)");
        }

        if (monthlyRentalsChart != null) {
            monthlyRentalsChart.setAnimated(false);
            monthlyRentalsChart.setTitle("Locations par mois");
            monthlyRentalsChart.getXAxis().setLabel("Mois");
            monthlyRentalsChart.getYAxis().setLabel("Nombre de locations");
        }

        if (clientGrowthChart != null) {
            clientGrowthChart.setAnimated(false);
            clientGrowthChart.setTitle("Croissance des clients");
            clientGrowthChart.getXAxis().setLabel("Mois");
            clientGrowthChart.getYAxis().setLabel("Nombre de clients");
        }
    }

    /**
     * Load dashboard data asynchronously with caching
     */
    private void loadDashboardDataAsync() {
        if (isRefreshing) return;
        
        isRefreshing = true;

        // Check cache first
        DashboardData cachedData = cacheManager.get("statistics", "dashboard_summary", DashboardData.class);
        if (cachedData != null && cachedData.isValid()) {
            updateUIWithData(cachedData);
            isRefreshing = false;
            return;
        }

        // Load data in background
        CompletableFuture<DashboardData> dataFuture = taskManager.executeBackground("dashboard_load", () -> {
            try {
                return loadDashboardData();
            } catch (Exception e) {
                System.err.println("Error loading dashboard data: " + e.getMessage());
                return new DashboardData(); // Return empty data on error
            }
        });

        dataFuture.thenAccept(data -> {
            Platform.runLater(() -> {
                updateUIWithData(data);
                // Cache the data
                cacheManager.put("statistics", "dashboard_summary", data);
                isRefreshing = false;
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                System.err.println("Failed to load dashboard data: " + throwable.getMessage());
                isRefreshing = false;
            });
            return null;
        });
    }

    /**
     * Load dashboard data from database
     */
    private DashboardData loadDashboardData() {
        DashboardData data = new DashboardData();

        try {
            // Load basic statistics
            List<Vehicule> vehicles = vehiculeDAO.findAll();
            List<Client> clients = clientDAO.findAll();
            List<Location> locations = locationDAO.findAll();
            List<Paiement> payments = paiementDAO.findAll();

            // Calculate vehicle statistics
            data.totalVehicles = vehicles.size();
            data.availableVehicles = (int) vehicles.stream()
                .filter(v -> "disponible".equalsIgnoreCase(v.getEtat()))
                .count();
            data.rentedVehicles = (int) vehicles.stream()
                .filter(v -> "loué".equalsIgnoreCase(v.getEtat()) || "loue".equalsIgnoreCase(v.getEtat()))
                .count();
            data.maintenanceVehicles = (int) vehicles.stream()
                .filter(v -> "en panne".equalsIgnoreCase(v.getEtat()) || "maintenance".equalsIgnoreCase(v.getEtat()))
                .count();

            // Calculate client and rental statistics
            data.totalClients = clients.size();
            data.activeRentals = (int) locations.stream()
                .filter(l -> l.getDateFinReelle() == null)
                .count();

            // Calculate revenue
            LocalDate today = LocalDate.now();
            LocalDate monthStart = today.withDayOfMonth(1);

            data.todayRevenue = payments.stream()
                .filter(p -> p.getDatePaiement() != null && p.getDatePaiement().equals(today))
                .mapToDouble(p -> p.getMontant() != null ? p.getMontant() : 0.0)
                .sum();

            data.monthRevenue = payments.stream()
                .filter(p -> p.getDatePaiement() != null && 
                           !p.getDatePaiement().isBefore(monthStart) && 
                           !p.getDatePaiement().isAfter(today))
                .mapToDouble(p -> p.getMontant() != null ? p.getMontant() : 0.0)
                .sum();

            // Calculate pending and overdue returns
            data.pendingReturns = (int) locations.stream()
                .filter(l -> l.getDateFinReelle() == null && 
                           l.getDateFinPrevue() != null && 
                           !l.getDateFinPrevue().isBefore(today))
                .count();

            data.overdueReturns = (int) locations.stream()
                .filter(l -> l.getDateFinReelle() == null && 
                           l.getDateFinPrevue() != null && 
                           l.getDateFinPrevue().isBefore(today))
                .count();

            // Prepare chart data
            data.vehicleStatusData = prepareVehicleStatusData(vehicles);
            data.revenueData = prepareRevenueData(payments);
            data.monthlyRentalsData = prepareMonthlyRentalsData(locations);
            data.clientGrowthData = prepareClientGrowthData(clients);

            // Recent activities and alerts
            data.recentActivities = prepareRecentActivities(locations, payments);
            data.upcomingReturns = prepareUpcomingReturns(locations);
            data.alerts = prepareAlerts(locations, vehicles);

            data.timestamp = System.currentTimeMillis();

        } catch (Exception e) {
            System.err.println("Error loading dashboard data: " + e.getMessage());
            e.printStackTrace();
        }

        return data;
    }

    /**
     * Update UI with loaded data
     */
    private void updateUIWithData(DashboardData data) {
        try {
            // Update statistics labels
            updateLabel(lblTotalVehicles, String.valueOf(data.totalVehicles));
            updateLabel(lblAvailableVehicles, String.valueOf(data.availableVehicles));
            updateLabel(lblRentedVehicles, String.valueOf(data.rentedVehicles));
            updateLabel(lblMaintenanceVehicles, String.valueOf(data.maintenanceVehicles));
            updateLabel(lblTotalClients, String.valueOf(data.totalClients));
            updateLabel(lblActiveRentals, String.valueOf(data.activeRentals));
            updateLabel(lblTodayRevenue, String.format("%.2f DH", data.todayRevenue));
            updateLabel(lblMonthRevenue, String.format("%.2f DH", data.monthRevenue));
            updateLabel(lblPendingReturns, String.valueOf(data.pendingReturns));
            updateLabel(lblOverdueReturns, String.valueOf(data.overdueReturns));

            // Update charts
            updateCharts(data);

            // Update activity containers
            updateRecentActivities(data.recentActivities);
            updateUpcomingReturns(data.upcomingReturns);
            updateAlerts(data.alerts);

        } catch (Exception e) {
            System.err.println("Error updating UI: " + e.getMessage());
        }
    }

    private void updateLabel(Label label, String text) {
        if (label != null) {
            label.setText(text);
        }
    }

    /**
     * Update charts with data
     */
    private void updateCharts(DashboardData data) {
        // Update vehicle status chart
        if (vehicleStatusChart != null && data.vehicleStatusData != null) {
            vehicleStatusChart.setData(data.vehicleStatusData);
        }

        // Update revenue chart
        if (revenueChart != null && data.revenueData != null) {
            revenueChart.getData().clear();
            revenueChart.getData().add(data.revenueData);
        }

        // Update monthly rentals chart
        if (monthlyRentalsChart != null && data.monthlyRentalsData != null) {
            monthlyRentalsChart.getData().clear();
            monthlyRentalsChart.getData().add(data.monthlyRentalsData);
        }

        // Update client growth chart
        if (clientGrowthChart != null && data.clientGrowthData != null) {
            clientGrowthChart.getData().clear();
            clientGrowthChart.getData().add(data.clientGrowthData);
        }
    }

    /**
     * Prepare vehicle status data for pie chart
     */
    private ObservableList<PieChart.Data> prepareVehicleStatusData(List<Vehicule> vehicles) {
        Map<String, Long> statusCounts = vehicles.stream()
            .collect(Collectors.groupingBy(
                v -> v.getEtat() != null ? v.getEtat() : "Inconnu",
                Collectors.counting()
            ));

        return FXCollections.observableArrayList(
            statusCounts.entrySet().stream()
                .map(entry -> new PieChart.Data(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList())
        );
    }

    /**
     * Prepare revenue data for line chart
     */
    private XYChart.Series<String, Number> prepareRevenueData(List<Paiement> payments) {
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Revenus quotidiens");

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(29);

        Map<LocalDate, Double> dailyRevenue = payments.stream()
            .filter(p -> p.getDatePaiement() != null && 
                        !p.getDatePaiement().isBefore(startDate) && 
                        !p.getDatePaiement().isAfter(endDate))
            .collect(Collectors.groupingBy(
                Paiement::getDatePaiement,
                Collectors.summingDouble(p -> p.getMontant() != null ? p.getMontant() : 0.0)
            ));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM");
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            double revenue = dailyRevenue.getOrDefault(date, 0.0);
            series.getData().add(new XYChart.Data<>(date.format(formatter), revenue));
        }

        return series;
    }

    /**
     * Prepare monthly rentals data
     */
    private XYChart.Series<String, Number> prepareMonthlyRentalsData(List<Location> locations) {
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Locations mensuelles");

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(11).withDayOfMonth(1);

        Map<String, Long> monthlyRentals = locations.stream()
            .filter(l -> l.getDateDebut() != null && 
                        !l.getDateDebut().isBefore(startDate) && 
                        !l.getDateDebut().isAfter(endDate))
            .collect(Collectors.groupingBy(
                l -> l.getDateDebut().format(DateTimeFormatter.ofPattern("MM/yyyy")),
                Collectors.counting()
            ));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/yyyy");
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusMonths(1)) {
            String monthKey = date.format(formatter);
            long count = monthlyRentals.getOrDefault(monthKey, 0L);
            series.getData().add(new XYChart.Data<>(monthKey, count));
        }

        return series;
    }

    /**
     * Prepare client growth data
     */
    private XYChart.Series<String, Number> prepareClientGrowthData(List<Client> clients) {
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Croissance des clients");

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(11).withDayOfMonth(1);

        Map<String, Long> monthlyClients = clients.stream()
            .filter(c -> c.getDateInscription() != null && 
                        !c.getDateInscription().isBefore(startDate) && 
                        !c.getDateInscription().isAfter(endDate))
            .collect(Collectors.groupingBy(
                c -> c.getDateInscription().format(DateTimeFormatter.ofPattern("MM/yyyy")),
                Collectors.counting()
            ));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/yyyy");
        long cumulativeCount = 0;
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusMonths(1)) {
            String monthKey = date.format(formatter);
            cumulativeCount += monthlyClients.getOrDefault(monthKey, 0L);
            series.getData().add(new XYChart.Data<>(monthKey, cumulativeCount));
        }

        return series;
    }

    /**
     * Prepare recent activities
     */
    private List<String> prepareRecentActivities(List<Location> locations, List<Paiement> payments) {
        List<String> activities = new ArrayList<>();

        // Recent rentals
        locations.stream()
            .filter(l -> l.getDateDebut() != null && 
                        l.getDateDebut().isAfter(LocalDate.now().minusDays(7)))
            .sorted((l1, l2) -> l2.getDateDebut().compareTo(l1.getDateDebut()))
            .limit(5)
            .forEach(l -> activities.add(String.format("Nouvelle location: %s %s - %s %s",
                l.getClient().getPrenom(), l.getClient().getNom(),
                l.getVehicule().getMarque(), l.getVehicule().getModele())));

        // Recent payments
        payments.stream()
            .filter(p -> p.getDatePaiement() != null && 
                        p.getDatePaiement().isAfter(LocalDate.now().minusDays(7)))
            .sorted((p1, p2) -> p2.getDatePaiement().compareTo(p1.getDatePaiement()))
            .limit(3)
            .forEach(p -> activities.add(String.format("Paiement reçu: %.2f DH", p.getMontant())));

        return activities.stream().limit(8).collect(Collectors.toList());
    }

    /**
     * Prepare upcoming returns
     */
    private List<String> prepareUpcomingReturns(List<Location> locations) {
        LocalDate today = LocalDate.now();
        LocalDate nextWeek = today.plusDays(7);

        return locations.stream()
            .filter(l -> l.getDateFinReelle() == null && 
                        l.getDateFinPrevue() != null && 
                        !l.getDateFinPrevue().isBefore(today) && 
                        !l.getDateFinPrevue().isAfter(nextWeek))
            .sorted(Comparator.comparing(Location::getDateFinPrevue))
            .limit(10)
            .map(l -> String.format("%s - %s %s (%s %s)",
                l.getDateFinPrevue().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
                l.getClient().getPrenom(), l.getClient().getNom(),
                l.getVehicule().getMarque(), l.getVehicule().getModele()))
            .collect(Collectors.toList());
    }

    /**
     * Prepare alerts
     */
    private List<String> prepareAlerts(List<Location> locations, List<Vehicule> vehicles) {
        List<String> alerts = new ArrayList<>();
        LocalDate today = LocalDate.now();

        // Overdue returns
        long overdueCount = locations.stream()
            .filter(l -> l.getDateFinReelle() == null && 
                        l.getDateFinPrevue() != null && 
                        l.getDateFinPrevue().isBefore(today))
            .count();

        if (overdueCount > 0) {
            alerts.add(String.format("⚠️ %d retour(s) en retard", overdueCount));
        }

        // Vehicles in maintenance
        long maintenanceCount = vehicles.stream()
            .filter(v -> "en panne".equalsIgnoreCase(v.getEtat()) || 
                        "maintenance".equalsIgnoreCase(v.getEtat()))
            .count();

        if (maintenanceCount > 0) {
            alerts.add(String.format("🔧 %d véhicule(s) en maintenance", maintenanceCount));
        }

        // Low availability
        long availableCount = vehicles.stream()
            .filter(v -> "disponible".equalsIgnoreCase(v.getEtat()))
            .count();

        if (availableCount < 5) {
            alerts.add(String.format("📉 Faible disponibilité: %d véhicule(s)", availableCount));
        }

        return alerts;
    }

    /**
     * Update recent activities container
     */
    private void updateRecentActivities(List<String> activities) {
        if (recentActivitiesContainer != null) {
            recentActivitiesContainer.getChildren().clear();
            
            activities.forEach(activity -> {
                Label activityLabel = new Label(activity);
                activityLabel.setWrapText(true);
                activityLabel.setStyle("-fx-padding: 5; -fx-text-fill: #333;");
                recentActivitiesContainer.getChildren().add(activityLabel);
            });
        }
    }

    /**
     * Update upcoming returns container
     */
    private void updateUpcomingReturns(List<String> returns) {
        if (upcomingReturnsContainer != null) {
            upcomingReturnsContainer.getChildren().clear();
            
            returns.forEach(returnInfo -> {
                Label returnLabel = new Label(returnInfo);
                returnLabel.setWrapText(true);
                returnLabel.setStyle("-fx-padding: 5; -fx-text-fill: #333;");
                upcomingReturnsContainer.getChildren().add(returnLabel);
            });
        }
    }

    /**
     * Update alerts container
     */
    private void updateAlerts(List<String> alerts) {
        if (alertsContainer != null) {
            alertsContainer.getChildren().clear();
            
            alerts.forEach(alert -> {
                Label alertLabel = new Label(alert);
                alertLabel.setWrapText(true);
                alertLabel.setStyle("-fx-padding: 5; -fx-text-fill: #d32f2f; -fx-font-weight: bold;");
                alertsContainer.getChildren().add(alertLabel);
            });
        }
    }

    /**
     * Refresh dashboard data
     */
    public void refreshDashboardData() {
        // Clear cache and reload
        cacheManager.clear("statistics");
        loadDashboardDataAsync();
    }

    /**
     * Manual refresh method for UI button
     */
    @FXML
    private void handleRefresh() {
        refreshDashboardData();
    }

    /**
     * Data container class for dashboard information
     */
    private static class DashboardData {
        // Statistics
        int totalVehicles = 0;
        int availableVehicles = 0;
        int rentedVehicles = 0;
        int maintenanceVehicles = 0;
        int totalClients = 0;
        int activeRentals = 0;
        double todayRevenue = 0.0;
        double monthRevenue = 0.0;
        int pendingReturns = 0;
        int overdueReturns = 0;

        // Chart data
        ObservableList<PieChart.Data> vehicleStatusData;
        XYChart.Series<String, Number> revenueData;
        XYChart.Series<String, Number> monthlyRentalsData;
        XYChart.Series<String, Number> clientGrowthData;

        // Activity data
        List<String> recentActivities = new ArrayList<>();
        List<String> upcomingReturns = new ArrayList<>();
        List<String> alerts = new ArrayList<>();

        // Cache management
        long timestamp = System.currentTimeMillis();
        private static final long CACHE_VALIDITY = 300000; // 5 minutes

        boolean isValid() {
            return (System.currentTimeMillis() - timestamp) < CACHE_VALIDITY;
        }
    }
}

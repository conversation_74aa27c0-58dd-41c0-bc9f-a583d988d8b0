package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import dao.PaiementDAO;
import model.Paiement;
import java.util.List;
import java.util.stream.Collectors;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.layout.GridPane;
import javafx.geometry.Insets;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.ComboBox;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.Alert.AlertType;
import javafx.stage.FileChooser;
import java.util.Optional;
import java.time.LocalDate;
import javafx.application.Platform;
import util.ExportUtil;
import util.ExportOptions;
import java.io.File;
import javafx.beans.property.SimpleStringProperty;
import util.FXMLUtil;
import controller.NavigationController;

public class PaiementController {
    @FXML
    private TableView<Paiement> paiementTable;
    @FXML
    private TableColumn<Paiement, Long> idColumn;
    @FXML
    private TableColumn<Paiement, String> clientColumn;
    @FXML
    private TableColumn<Paiement, Double> montantColumn;
    @FXML
    private TableColumn<Paiement, String> dateColumn;
    @FXML
    private TableColumn<Paiement, String> statutColumn;
    @FXML
    private TextField searchField;
    @FXML
    private Label lblTotalCount;

    // Form elements
    @FXML private ComboBox<model.Client> txtClientForm;
    @FXML private TextField txtMontantForm;
    @FXML private DatePicker txtDateForm;
    @FXML private ComboBox<String> txtStatutForm;
    @FXML private ComboBox<String> txtMethodeForm;
    @FXML private Button btnSave;
    @FXML private Button btnCancel;

    // Enhanced payment fields
    @FXML private ComboBox<model.Location> txtLocationForm; // Select specific location/contract
    @FXML private TextField txtNumeroContratForm; // Contract number search
    @FXML private ComboBox<String> txtTypePaiementForm; // AVANCE, PARTIEL, SOLDE, COMPLET
    @FXML private TextField txtNumeroPaiementForm; // Payment sequence number
    @FXML private TextField txtMontantRestantForm; // Remaining amount
    @FXML private TextArea txtNotesForm; // Payment notes

    private final PaiementDAO paiementDAO = new PaiementDAO();
    private final dao.LocationDAO locationDAO = new dao.LocationDAO();
    private final dao.ClientDAO clientDAO = new dao.ClientDAO();
    private ObservableList<Paiement> paiementList;
    private Paiement selectedPaiement = null;
    private boolean isEditMode = false;

    @FXML
    public void initialize() {
        idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        clientColumn.setCellValueFactory(data -> {
            model.Location loc = data.getValue().getLocation();
            model.Client c = loc != null ? loc.getClient() : null;
            String name = c != null ? c.getNom() + " " + c.getPrenom() : "";
            return new javafx.beans.property.SimpleStringProperty(name);
        });
        montantColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleDoubleProperty(data.getValue().getMontant()).asObject());
        dateColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDatePaiement();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        statutColumn.setCellValueFactory(data -> {
            String statut = data.getValue().getStatut();
            return new javafx.beans.property.SimpleStringProperty(statut != null ? statut : "Payé");
        });
        // Custom cell factory and converter for txtClientForm
        if (txtClientForm != null) {
            txtClientForm.setCellFactory(cb -> new javafx.scene.control.ListCell<model.Client>() {
                @Override
                protected void updateItem(model.Client c, boolean empty) {
                    super.updateItem(c, empty);
                    if (empty || c == null) {
                        setText(null);
                    } else {
                        setText(c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : ""));
                    }
                }
            });
            txtClientForm.setButtonCell(new javafx.scene.control.ListCell<model.Client>() {
                @Override
                protected void updateItem(model.Client c, boolean empty) {
                    super.updateItem(c, empty);
                    if (empty || c == null) {
                        setText(null);
                    } else {
                        setText(c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : ""));
                    }
                }
            });
            txtClientForm.setConverter(new javafx.util.StringConverter<model.Client>() {
                @Override
                public String toString(model.Client c) {
                    if (c == null) return "";
                    return c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : "");
                }
                @Override
                public model.Client fromString(String s) { return null; }
            });
        }

        // Setup form combo boxes
        if (txtStatutForm != null) {
            txtStatutForm.setItems(FXCollections.observableArrayList("En attente", "Payé", "Remboursé"));
        }
        if (txtMethodeForm != null) {
            txtMethodeForm.setItems(FXCollections.observableArrayList("Espèces", "Carte bancaire", "Virement", "Chèque"));
        }

        // Setup new enhanced fields
        if (txtTypePaiementForm != null) {
            txtTypePaiementForm.setItems(FXCollections.observableArrayList("AVANCE", "PARTIEL", "SOLDE", "COMPLET"));
        }

        // Setup location combo box with contract numbers
        if (txtLocationForm != null) {
            txtLocationForm.setCellFactory(listView -> new javafx.scene.control.ListCell<model.Location>() {
                @Override
                protected void updateItem(model.Location location, boolean empty) {
                    super.updateItem(location, empty);
                    if (empty || location == null) {
                        setText(null);
                    } else {
                        String clientName = location.getClient() != null ?
                            location.getClient().getNom() + " " + location.getClient().getPrenom() : "N/A";
                        String vehicleName = location.getVehicule() != null ?
                            location.getVehicule().getMarque() + " " + location.getVehicule().getModele() : "N/A";
                        String contractNum = location.getNumeroContrat() != null ? location.getNumeroContrat() : "N/A";
                        setText(contractNum + " - " + clientName + " (" + vehicleName + ")");
                    }
                }
            });
            txtLocationForm.setButtonCell(new javafx.scene.control.ListCell<model.Location>() {
                @Override
                protected void updateItem(model.Location location, boolean empty) {
                    super.updateItem(location, empty);
                    if (empty || location == null) {
                        setText(null);
                    } else {
                        String clientName = location.getClient() != null ?
                            location.getClient().getNom() + " " + location.getClient().getPrenom() : "N/A";
                        String contractNum = location.getNumeroContrat() != null ? location.getNumeroContrat() : "N/A";
                        setText(contractNum + " - " + clientName);
                    }
                }
            });
        }

        // Load clients for the combo box
        if (txtClientForm != null) {
            try {
                List<model.Client> clients = clientDAO.findAll();
                txtClientForm.setItems(FXCollections.observableArrayList(clients));
            } catch (Exception e) {
                System.err.println("Error loading clients: " + e.getMessage());
            }
        }

        // Load locations for the combo box
        if (txtLocationForm != null) {
            try {
                List<model.Location> locations = locationDAO.findAll();
                txtLocationForm.setItems(FXCollections.observableArrayList(locations));
            } catch (Exception e) {
                System.err.println("Error loading locations: " + e.getMessage());
            }
        }

        // Add table selection listener
        paiementTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            try {
                if (newSelection != null) {
                    selectedPaiement = newSelection;
                    loadPaiementToForm(newSelection);
                    isEditMode = true;
                    if (btnSave != null) btnSave.setText("Modifier");
                } else {
                    clearForm();
                }
            } catch (Exception e) {
                System.err.println("Error in payment selection: " + e.getMessage());
                clearForm();
            }
        });

        loadPaiements();
        clearForm();
    }

    private void loadPaiements() {
        try {
            List<Paiement> list = paiementDAO.findAll();
            paiementList = FXCollections.observableArrayList(list);
            paiementTable.setItems(paiementList);

            // Update total count
            if (lblTotalCount != null) {
                lblTotalCount.setText("Total: " + list.size() + " paiements");
            }

            System.out.println("Loaded " + list.size() + " payments from database");
        } catch (Exception e) {
            System.err.println("Error loading payments: " + e.getMessage());
            e.printStackTrace();
            paiementList = FXCollections.observableArrayList();
            paiementTable.setItems(paiementList);
            if (lblTotalCount != null) {
                lblTotalCount.setText("Erreur de chargement");
            }
        }
    }

    private void loadPaiementToForm(Paiement paiement) {
        if (paiement == null) {
            clearForm();
            return;
        }

        try {
            // Load location's client if available
            if (txtClientForm != null && paiement.getLocation() != null && paiement.getLocation().getClient() != null) {
                txtClientForm.setValue(paiement.getLocation().getClient());
            }

            if (txtMontantForm != null) {
                txtMontantForm.setText(String.valueOf(paiement.getMontant()));
            }

            if (txtDateForm != null) {
                txtDateForm.setValue(paiement.getDatePaiement());
            }

            if (txtStatutForm != null) {
                txtStatutForm.setValue(paiement.getStatut() != null ? paiement.getStatut() : "Payé");
            }

            if (txtMethodeForm != null) {
                txtMethodeForm.setValue(paiement.getMethodePaiement() != null ? paiement.getMethodePaiement() : "Carte bancaire");
            }
        } catch (Exception e) {
            System.err.println("Error loading payment to form: " + e.getMessage());
        }
    }

    private void clearForm() {
        try {
            if (txtClientForm != null) txtClientForm.setValue(null);
            if (txtMontantForm != null) txtMontantForm.clear();
            if (txtDateForm != null) txtDateForm.setValue(null);
            if (txtStatutForm != null) txtStatutForm.setValue(null);
            if (txtMethodeForm != null) txtMethodeForm.setValue(null);

            selectedPaiement = null;
            isEditMode = false;
            if (btnSave != null) btnSave.setText("Enregistrer");
        } catch (Exception e) {
            System.err.println("Error clearing form: " + e.getMessage());
        }
    }

    @FXML
    private void handleAjouter() {
        clearForm();
        isEditMode = false;
        if (btnSave != null) btnSave.setText("Enregistrer");

        // Set default values
        if (txtDateForm != null) {
            txtDateForm.setValue(java.time.LocalDate.now());
        }
        if (txtStatutForm != null) {
            txtStatutForm.setValue("Payé");
        }
        if (txtMethodeForm != null) {
            txtMethodeForm.setValue("Carte bancaire");
        }
    }

    @FXML
    private void handleModifier() {
        Paiement selected = paiementTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            loadPaiementToForm(selected);
            isEditMode = true;
            if (btnSave != null) btnSave.setText("Modifier");
        } else {
            showAlert("Veuillez sélectionner un paiement à modifier", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSupprimer() {
        Paiement selected = paiementTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            Alert alert = new Alert(AlertType.CONFIRMATION);
            alert.setTitle("Confirmer la suppression");
            alert.setHeaderText("Supprimer le paiement");
            alert.setContentText("Êtes-vous sûr de vouloir supprimer ce paiement ?");

            alert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        paiementDAO.delete(selected);
                        loadPaiements();
                        clearForm();
                        showAlert("Paiement supprimé avec succès", AlertType.INFORMATION);
                    } catch (Exception e) {
                        showAlert("Erreur lors de la suppression: " + e.getMessage(), AlertType.ERROR);
                    }
                }
            });
        } else {
            showAlert("Veuillez sélectionner un paiement à supprimer", AlertType.WARNING);
        }
    }

    @FXML
    private void handleRechercher() {
        String keyword = searchField.getText().toLowerCase();
        List<Paiement> filtered = paiementDAO.findAll().stream()
                .filter(p -> {
                    model.Location loc = p.getLocation();
                    model.Client c = loc != null ? loc.getClient() : null;
                    String client = c != null ? c.getNom() + " " + c.getPrenom() : "";
                    String statut = p.getStatut() != null ? p.getStatut() : "Payé";
                    String methode = p.getMethodePaiement() != null ? p.getMethodePaiement() : "";
                    String montant = String.valueOf(p.getMontant());
                    return client.toLowerCase().contains(keyword)
                        || statut.toLowerCase().contains(keyword)
                        || methode.toLowerCase().contains(keyword)
                        || montant.contains(keyword);
                })
                .collect(Collectors.toList());
        paiementList.setAll(filtered);
    }

    @FXML
    private void handleRefresh() {
        loadPaiements();
        searchField.clear();
    }

    @FXML
    private void handleExport() {
        // Create export dialog
        Dialog<ExportOptions> dialog = new Dialog<>();
        dialog.setTitle("Exporter les paiements");
        dialog.setHeaderText("Choisissez les options d'export");

        // Set the button types
        ButtonType exportButtonType = new ButtonType("Exporter", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(exportButtonType, ButtonType.CANCEL);

        // Create the custom content
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        ComboBox<String> formatCombo = new ComboBox<>();
        formatCombo.getItems().addAll("CSV", "PDF");
        formatCombo.setValue("CSV");

        ComboBox<String> periodCombo = new ComboBox<>();
        periodCombo.getItems().addAll("Tous les paiements", "Période personnalisée");
        periodCombo.setValue("Tous les paiements");

        DatePicker startDate = new DatePicker();
        DatePicker endDate = new DatePicker();
        startDate.setDisable(true);
        endDate.setDisable(true);

        periodCombo.valueProperty().addListener((obs, oldVal, newVal) -> {
            boolean custom = "Période personnalisée".equals(newVal);
            startDate.setDisable(!custom);
            endDate.setDisable(!custom);
        });

        grid.add(new Label("Format:"), 0, 0);
        grid.add(formatCombo, 1, 0);
        grid.add(new Label("Période:"), 0, 1);
        grid.add(periodCombo, 1, 1);
        grid.add(new Label("Date début:"), 0, 2);
        grid.add(startDate, 1, 2);
        grid.add(new Label("Date fin:"), 0, 3);
        grid.add(endDate, 1, 3);

        dialog.getDialogPane().setContent(grid);

        // Request focus on the format combo by default
        Platform.runLater(() -> formatCombo.requestFocus());

        // Convert the result when the export button is clicked
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == exportButtonType) {
                return new ExportOptions(
                    formatCombo.getValue(),
                    periodCombo.getValue(),
                    startDate.getValue(),
                    endDate.getValue()
                );
            }
            return null;
        });

        Optional<ExportOptions> result = dialog.showAndWait();

        result.ifPresent(options -> {
            try {
                // File chooser
                FileChooser fileChooser = new FileChooser();
                fileChooser.setTitle("Sauvegarder l'export des paiements");
                
                String extension = "CSV".equals(options.getFormat()) ? "*.csv" : "*.pdf";
                String formatName = "CSV".equals(options.getFormat()) ? "CSV Files" : "PDF Files";
                fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter(formatName, extension));
                
                File file = fileChooser.showSaveDialog(paiementTable.getScene().getWindow());
                
                if (file != null) {
                    // Filter data based on period
                    List<Paiement> dataToExport = paiementList;
                    if ("Période personnalisée".equals(options.getPeriod()) && options.getStartDate() != null && options.getEndDate() != null) {
                        dataToExport = paiementList.stream()
                            .filter(p -> {
                                if (p.getDatePaiement() == null) return false;
                                return !p.getDatePaiement().isBefore(options.getStartDate()) &&
                                       !p.getDatePaiement().isAfter(options.getEndDate());
                            })
                            .collect(Collectors.toList());
                    }

                    String[] headers = {"ID", "Client", "Montant (DH)", "Date", "Statut", "Méthode"};
                    
                    if ("CSV".equals(options.getFormat())) {
                        ExportUtil.exportToCSV(dataToExport, headers, paiement -> {
                            model.Location loc = paiement.getLocation();
                            model.Client c = loc != null ? loc.getClient() : null;
                            String clientName = c != null ? c.getNom() + " " + c.getPrenom() : "";
                            return new String[]{
                                String.valueOf(paiement.getId()),
                                clientName,
                                String.valueOf(paiement.getMontant()),
                                paiement.getDatePaiement() != null ? paiement.getDatePaiement().toString() : "",
                                paiement.getStatut() != null ? paiement.getStatut() : "Payé",
                                paiement.getMethodePaiement() != null ? paiement.getMethodePaiement() : "Carte bancaire"
                            };
                        }, file.getAbsolutePath());
                    } else {
                        ExportUtil.exportToPDF(dataToExport, headers, paiement -> {
                            model.Location loc = paiement.getLocation();
                            model.Client c = loc != null ? loc.getClient() : null;
                            String clientName = c != null ? c.getNom() + " " + c.getPrenom() : "";
                            return new String[]{
                                String.valueOf(paiement.getId()),
                                clientName,
                                String.valueOf(paiement.getMontant()),
                                paiement.getDatePaiement() != null ? paiement.getDatePaiement().toString() : "",
                                paiement.getStatut() != null ? paiement.getStatut() : "Payé",
                                paiement.getMethodePaiement() != null ? paiement.getMethodePaiement() : "Carte bancaire"
                            };
                        }, file.getAbsolutePath());
                    }

                    Alert alert = new Alert(AlertType.INFORMATION);
                    alert.setTitle("Export réussi");
                    alert.setHeaderText(null);
                    alert.setContentText("Export réussi!\nFichier sauvegardé: " + file.getAbsolutePath());
                    alert.showAndWait();
                }
            } catch (Exception e) {
                Alert alert = new Alert(AlertType.ERROR);
                alert.setTitle("Erreur d'export");
                alert.setHeaderText(null);
                alert.setContentText("Erreur lors de l'export: " + e.getMessage());
                alert.showAndWait();
            }
        });
    }



    @FXML
    private void handleClearFilters() {
        searchField.clear();
        loadPaiements();
    }

    @FXML
    private void handleCancel() {
        clearForm();
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("LocationV1");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    @FXML
    private void handleSave() {
        try {
            // Validate form fields
            if (txtMontantForm == null || txtMontantForm.getText().trim().isEmpty()) {
                showAlert("Veuillez saisir le montant", AlertType.WARNING);
                return;
            }

            if (txtDateForm == null || txtDateForm.getValue() == null) {
                showAlert("Veuillez sélectionner une date", AlertType.WARNING);
                return;
            }

            double montant;
            try {
                montant = Double.parseDouble(txtMontantForm.getText().trim());
                if (montant <= 0) {
                    showAlert("Le montant doit être positif", AlertType.WARNING);
                    return;
                }
            } catch (NumberFormatException e) {
                showAlert("Montant invalide", AlertType.WARNING);
                return;
            }

            // Create or update payment
            Paiement paiement;
            if (isEditMode && selectedPaiement != null) {
                paiement = selectedPaiement;
            } else {
                paiement = new Paiement();
                // For new payments, we need a location - this should be selected from available locations
                // For now, we'll show a message that location selection is needed
                if (txtClientForm == null || txtClientForm.getValue() == null) {
                    showAlert("Veuillez sélectionner un client", AlertType.WARNING);
                    return;
                }

                // Find an active location for this client
                model.Client selectedClient = txtClientForm.getValue();
                List<model.Location> clientLocations = locationDAO.findByClientId(selectedClient.getId());
                if (clientLocations.isEmpty()) {
                    showAlert("Aucune location trouvée pour ce client", AlertType.WARNING);
                    return;
                }

                // Use the most recent location
                paiement.setLocation(clientLocations.get(0));
            }

            // Set payment data
            paiement.setMontant(montant);
            paiement.setDatePaiement(txtDateForm.getValue());
            paiement.setStatut(txtStatutForm != null && txtStatutForm.getValue() != null ?
                txtStatutForm.getValue() : "Payé");
            paiement.setMethodePaiement(txtMethodeForm != null && txtMethodeForm.getValue() != null ?
                txtMethodeForm.getValue() : "Carte bancaire");

            // Save to database
            paiementDAO.save(paiement);

            // Refresh table and clear form
            loadPaiements();
            clearForm();

            String message = isEditMode ? "Paiement modifié avec succès" : "Paiement ajouté avec succès";
            showAlert(message, AlertType.INFORMATION);

        } catch (Exception e) {
            showAlert("Erreur lors de l'enregistrement: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handlePaymentHistory() {
        try {
            System.out.println("Opening payment history...");

            // Create a comprehensive payment history dialog
            Dialog<Void> dialog = new Dialog<>();
            dialog.setTitle("Historique Détaillé des Paiements");
            dialog.setHeaderText("Analyse complète des paiements et transactions");

            // Create content
            javafx.scene.layout.VBox content = new javafx.scene.layout.VBox(15);
            content.setPadding(new Insets(20));
            content.setStyle("-fx-background-color: #f8fafc;");

            // Statistics section
            javafx.scene.layout.HBox statsBox = new javafx.scene.layout.HBox(20);
            statsBox.setAlignment(javafx.geometry.Pos.CENTER);

            // Get payment statistics
            List<Paiement> allPayments = paiementDAO.findAll();
            double totalRevenue = allPayments.stream().mapToDouble(Paiement::getMontant).sum();
            long completedPayments = allPayments.stream().filter(p -> "Payé".equals(p.getStatut())).count();
            long pendingPayments = allPayments.stream().filter(p -> "En attente".equals(p.getStatut())).count();

            // Create stat cards
            javafx.scene.layout.VBox totalCard = createStatCard("💰 Revenus Total", String.format("%.2f DH", totalRevenue), "#10b981");
            javafx.scene.layout.VBox completedCard = createStatCard("✅ Paiements Réalisés", String.valueOf(completedPayments), "#3b82f6");
            javafx.scene.layout.VBox pendingCard = createStatCard("⏳ En Attente", String.valueOf(pendingPayments), "#f59e0b");
            javafx.scene.layout.VBox totalCard2 = createStatCard("📊 Total Transactions", String.valueOf(allPayments.size()), "#8b5cf6");

            statsBox.getChildren().addAll(totalCard, completedCard, pendingCard, totalCard2);

            // Recent payments table
            Label recentLabel = new Label("Paiements Récents");
            recentLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a365d;");

            TableView<Paiement> historyTable = new TableView<>();
            historyTable.setPrefHeight(300);

            // Setup columns
            TableColumn<Paiement, String> clientCol = new TableColumn<>("Client");
            clientCol.setPrefWidth(150);
            clientCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getLocation() != null && cellData.getValue().getLocation().getClient() != null ?
                    cellData.getValue().getLocation().getClient().getNom() + " " + cellData.getValue().getLocation().getClient().getPrenom() :
                    "N/A"
                )
            );

            TableColumn<Paiement, String> montantCol = new TableColumn<>("Montant");
            montantCol.setPrefWidth(100);
            montantCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(String.format("%.2f DH", cellData.getValue().getMontant()))
            );

            TableColumn<Paiement, String> dateCol = new TableColumn<>("Date");
            dateCol.setPrefWidth(120);
            dateCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(
                    cellData.getValue().getDatePaiement() != null ?
                    cellData.getValue().getDatePaiement().toString() : "N/A"
                )
            );

            TableColumn<Paiement, String> statutCol = new TableColumn<>("Statut");
            statutCol.setPrefWidth(100);
            statutCol.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(cellData.getValue().getStatut())
            );

            historyTable.getColumns().addAll(clientCol, montantCol, dateCol, statutCol);

            // Load recent payments (last 20)
            List<Paiement> recentPayments = allPayments.stream()
                .sorted((p1, p2) -> {
                    if (p1.getDatePaiement() == null && p2.getDatePaiement() == null) return 0;
                    if (p1.getDatePaiement() == null) return 1;
                    if (p2.getDatePaiement() == null) return -1;
                    return p2.getDatePaiement().compareTo(p1.getDatePaiement());
                })
                .limit(20)
                .collect(Collectors.toList());

            historyTable.setItems(FXCollections.observableArrayList(recentPayments));

            content.getChildren().addAll(statsBox, recentLabel, historyTable);

            dialog.getDialogPane().setContent(content);
            dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);

            // Set dialog size
            dialog.getDialogPane().setPrefSize(800, 600);

            dialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Error opening payment history: " + e.getMessage());
            e.printStackTrace();

            Alert alert = new Alert(AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur lors de l'ouverture de l'historique");
            alert.setContentText("Une erreur s'est produite: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private javafx.scene.layout.VBox createStatCard(String title, String value, String color) {
        javafx.scene.layout.VBox card = new javafx.scene.layout.VBox(8);
        card.setAlignment(javafx.geometry.Pos.CENTER);
        card.setStyle("-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12; " +
                     "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-min-width: 160;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: bold;");

        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        // Use centralized navigation controller
        NavigationController.handleNavigation(event, (Stage) paiementTable.getScene().getWindow());
    }
}

package controller;

import javafx.fxml.FXML;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import model.Vehicule;
import controller.LoginController;
import model.User;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.stage.Stage;
import model.Admin;
import model.Agent;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import util.FXMLUtil;

public class VehiculeDetailController {
    @FXML
    private ImageView vehiculeImage;
    @FXML
    private Label marqueLabel;
    @FXML
    private Label modeleLabel;
    @FXML
    private Label immatriculationLabel;
    @FXML
    private Label etatLabel;
    @FXML
    private Label prixLabel;
    @FXML
    private Button btnLouer;
    @FXML
    private Button btnReserver;
    @FXML
    private Button btnHistorique;
    @FXML
    private Button btnDisponibilite;

    private Vehicule vehicule;

    public void setVehicule(Vehicule v) {
        this.vehicule = v;
        if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
            vehiculeImage.setImage(new Image(v.getPhotoUrl(), 320, 180, true, true));
        }
        marqueLabel.setText(v.getMarque());
        modeleLabel.setText(v.getModele());
        immatriculationLabel.setText("Immatriculation: " + v.getImmatriculation());
        etatLabel.setText("État: " + v.getEtat());
        prixLabel.setText(String.format("%.2f DH/jour", v.getPrixParJour()));
        Object user = LoginController.loggedInUser;
        btnLouer.setVisible(user instanceof Admin || user instanceof Agent);
    }

    @FXML
    private void handleLouer() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location_create.fxml"));
            Parent root = loader.load();
            LocationCreateController ctrl = loader.getController();
            ctrl.setVehicule(vehicule);
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));
            dialog.setTitle("Nouvelle Location");
            // Maximize window while avoiding taskbar overlap
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            dialog.setX(visualBounds.getMinX());
            dialog.setY(visualBounds.getMinY());
            dialog.setWidth(visualBounds.getWidth());
            dialog.setHeight(visualBounds.getHeight() * 0.95); // Use 95% to avoid taskbar
            dialog.showAndWait();
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
            alert.setTitle("Succès");
            alert.setHeaderText(null);
            alert.setContentText("Location créée avec succès !");
            alert.showAndWait();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleReserver() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/reserver_louer.fxml"));
            Parent root = loader.load();
            ReserverLouerController ctrl = loader.getController();
            ctrl.setVehicule(vehicule);
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));
            dialog.setTitle("Réserver / Louer");
            // Maximize window while avoiding taskbar overlap
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            dialog.setX(visualBounds.getMinX());
            dialog.setY(visualBounds.getMinY());
            dialog.setWidth(visualBounds.getWidth());
            dialog.setHeight(visualBounds.getHeight() * 0.95); // Use 95% to avoid taskbar
            dialog.showAndWait();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleHistorique() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/historique_locations_enhanced.fxml"));
            Parent root = loader.load();
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));

        } catch (Exception e) {

            FXMLUtil.showError("Impossible de charger l'historique: " + e.getMessage());
        }
    }

    @FXML
    private void handleDisponibilite() {
        try {
            // Use the enhanced main version
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/disponibilite.fxml"));
            Parent root = loader.load();
            DisponibiliteController ctrl = loader.getController();
            ctrl.setVehicule(vehicule);
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));
            dialog.setTitle("Calendrier de Disponibilité - " + vehicule.getMarque() + " " + vehicule.getModele());
            // Maximize window while avoiding taskbar overlap
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            dialog.setX(visualBounds.getMinX());
            dialog.setY(visualBounds.getMinY());
            dialog.setWidth(visualBounds.getWidth());
            dialog.setHeight(visualBounds.getHeight() * 0.95); // Use 95% to avoid taskbar
            dialog.showAndWait();
        } catch (Exception e) {
            System.err.println("Error loading disponibilite: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

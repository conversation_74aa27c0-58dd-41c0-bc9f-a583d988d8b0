package controller;

import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;
import model.Client;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.scene.Scene;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import javafx.scene.control.Button;
import javafx.embed.swing.SwingFXUtils;
import javax.imageio.ImageIO;

public class ClientDetailController {
    @FXML private Label lblNom;
    @FXML private Label lblPrenom;
    @FXML private Label lblCin;
    @FXML private Label lblTelephone;
    @FXML private Label lblEmail;
    @FXML private Label lblPermis;
    @FXML private Label lblAdresse;
    @FXML private ImageView imgPermisRecto;
    @FXML private ImageView imgPermisVerso;
    @FXML private ImageView imgCinRecto;
    @FXML private ImageView imgCinVerso;
    @FXML private VBox root;

    public void setClient(Client client) {
        if (client == null) return;
        lblNom.setText(client.getNom());
        lblPrenom.setText(client.getPrenom());
        lblCin.setText(client.getCin());
        lblTelephone.setText(client.getTelephone());
        lblEmail.setText(client.getEmail());
        lblPermis.setText(client.getPermis());
        lblAdresse.setText(client.getAdresse());
        loadImageToView(imgPermisRecto, client.getPermisRecto());
        loadImageToView(imgPermisVerso, client.getPermisVerso());
        loadImageToView(imgCinRecto, client.getCinRecto());
        loadImageToView(imgCinVerso, client.getCinVerso());
    }

    private void loadImageToView(ImageView view, String path) {
        if (path != null && !path.isEmpty()) {
            try {
                view.setImage(new Image(path));
            } catch (Exception e) { view.setImage(null); }
        } else {
            view.setImage(null);
        }
    }

    public void closeWindow() {
        javafx.stage.Stage stage = (javafx.stage.Stage) root.getScene().getWindow();
        stage.close();
    }

    @FXML
    private void handleZoomPermisRecto() { zoomImage(imgPermisRecto.getImage(), "Permis Recto"); }
    @FXML
    private void handleZoomPermisVerso() { zoomImage(imgPermisVerso.getImage(), "Permis Verso"); }
    @FXML
    private void handleZoomCinRecto() { zoomImage(imgCinRecto.getImage(), "CIN Recto"); }
    @FXML
    private void handleZoomCinVerso() { zoomImage(imgCinVerso.getImage(), "CIN Verso"); }

    private void zoomImage(Image image, String title) {
        if (image == null) return;
        Stage stage = new Stage();
        stage.initModality(Modality.APPLICATION_MODAL);
        VBox box = new VBox(10);
        box.setStyle("-fx-background-color: white; -fx-padding: 24;");
        ImageView view = new ImageView(image);
        view.setPreserveRatio(true);
        view.setFitWidth(600);
        view.setFitHeight(400);
        Button close = new Button("Fermer");
        close.setStyle("-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 7; -fx-padding: 8 24;");
        close.setOnAction(e -> stage.close());
        box.getChildren().addAll(view, close);
        Scene scene = new Scene(box);
        stage.setTitle(title);
        stage.setScene(scene);
        // Maximize window while avoiding taskbar overlap
        Screen screen = Screen.getPrimary();
        Rectangle2D visualBounds = screen.getVisualBounds();
        stage.setX(visualBounds.getMinX());
        stage.setY(visualBounds.getMinY());
        stage.setWidth(visualBounds.getWidth());
        stage.setHeight(visualBounds.getHeight() * 0.95); // Use 95% to avoid taskbar
        stage.showAndWait();
    }

    @FXML
    private void handleDownloadPermisRecto() { downloadImage(imgPermisRecto.getImage(), "permis_recto.png"); }
    @FXML
    private void handleDownloadPermisVerso() { downloadImage(imgPermisVerso.getImage(), "permis_verso.png"); }
    @FXML
    private void handleDownloadCinRecto() { downloadImage(imgCinRecto.getImage(), "cin_recto.png"); }
    @FXML
    private void handleDownloadCinVerso() { downloadImage(imgCinVerso.getImage(), "cin_verso.png"); }

    private void downloadImage(Image image, String defaultName) {
        if (image == null) return;
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Enregistrer l'image");
        fileChooser.setInitialFileName(defaultName);
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("PNG Image", "*.png"));
        File file = fileChooser.showSaveDialog(null);
        if (file != null) {
            try (InputStream is = new FileInputStream(image.getUrl().replace("file:/", "/"));
                 OutputStream os = new FileOutputStream(file)) {
                byte[] buffer = new byte[4096];
                int len;
                while ((len = is.read(buffer)) > 0) {
                    os.write(buffer, 0, len);
                }
            } catch (Exception e) {
                // fallback: snapshot
                try {
                    javafx.embed.swing.SwingFXUtils.fromFXImage(image, null);
                    javax.imageio.ImageIO.write(javafx.embed.swing.SwingFXUtils.fromFXImage(image, null), "png", file);
                } catch (Exception ex) {
                    // ignore
                }
            }
        }
    }
}

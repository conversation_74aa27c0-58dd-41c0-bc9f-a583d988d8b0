package controller;

import javafx.animation.FadeTransition;
import javafx.concurrent.Task;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.image.PixelWriter;
import javafx.scene.image.WritableImage;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.stage.Stage;
import javafx.stage.FileChooser;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.geometry.Pos;
import javafx.geometry.Insets;
import javafx.application.Platform;
import javafx.util.Duration;
import model.Vehicule;
import dao.VehiculeDAO;
import controller.DisponibiliteController;
import util.FXMLUtil;
import util.MemoryManager;

import java.util.List;
import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.IOException;

public class CatalogueController {
    // CSS Style Classes
    private static final String CARD_STYLE = "vehicle-card";
    private static final String CARD_HOVER_STYLE = "vehicle-card-hover";
    private static final String STATUS_BADGE_STYLE = "status-badge";
    private static final String LIST_ROW_STYLE = "list-row";
    private static final String LIST_ROW_HOVER_STYLE = "list-row-hover";
    private static final String DETAIL_BUTTON_STYLE = "detail-button";
    private static final String RENT_BUTTON_STYLE = "rent-button";

    // Main containers
    @FXML private FlowPane cataloguePane;
    @FXML private VBox listViewPane;
    @FXML private ScrollPane cardViewContainer;
    @FXML private ScrollPane listViewContainer;
    @FXML private VBox emptyState;

    // Filter controls
    @FXML private ComboBox<String> marqueFilter;
    @FXML private ComboBox<String> etatFilter;
    @FXML private ComboBox<String> carburantFilter;
    @FXML private TextField prixMaxFilter;
    @FXML private TextField searchField;

    // Statistics labels
    @FXML private Label lblTotalCount;
    @FXML private Label lblAvailableCount;
    @FXML private Label lblRentedCount;
    @FXML private Label lblMaintenanceCount;
    @FXML private Label lblTotalVehicles;

    // Action buttons
    @FXML private Button btnViewMode;
    @FXML private Button btnRefresh;
    @FXML private Button btnExport;
    @FXML private Button btnAddVehicle;
    @FXML private Button btnClearFilters;

    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private ObservableList<Vehicule> allVehicles;
    private ObservableList<Vehicule> filteredVehicles;
    private boolean isListView = false;
    private Image placeholderImage;
    
    // Performance optimizations
    private final ConcurrentHashMap<String, Image> imageCache = new ConcurrentHashMap<>();
    private volatile boolean isLoading = false;
    private Task<Void> currentLoadTask;
    
    // Debouncing for search
    private javafx.animation.Timeline searchDebounceTimer;

    @FXML
    private void handleSearch() {
        debounceSearch();
    }
    
    /**
     * Debounced search to prevent excessive filtering
     */
    private void debounceSearch() {
        if (searchDebounceTimer != null) {
            searchDebounceTimer.stop();
        }
        
        searchDebounceTimer = new javafx.animation.Timeline(
            new javafx.animation.KeyFrame(Duration.millis(300), e -> applyFilters())
        );
        searchDebounceTimer.play();
    }

    @FXML
    private void toggleViewMode() {
        isListView = !isListView;

        if (isListView) {
            cardViewContainer.setVisible(false);
            listViewContainer.setVisible(true);
            btnViewMode.setText("🔲 Vue Cartes");
            displayVehiclesInListAsync();
        } else {
            listViewContainer.setVisible(false);
            cardViewContainer.setVisible(true);
            btnViewMode.setText("📋 Vue Liste");
            displayVehiclesInCardsAsync();
        }
    }

    @FXML
    private void handleRefresh() {
        loadCatalogueAsync();
    }

    @FXML
    private void handleApplyFilters() {
        applyFilters();
    }

    /**
     * Optimized filtering with background processing
     */
    private void applyFilters() {
        if (allVehicles == null || isLoading) {
            return;
        }

        Task<List<Vehicule>> filterTask = new Task<List<Vehicule>>() {
            @Override
            protected List<Vehicule> call() throws Exception {
                return allVehicles.stream()
                    .filter(CatalogueController.this::matchesFilters)
                    .collect(Collectors.toList());
            }
        };

        filterTask.setOnSucceeded(e -> {
            filteredVehicles.setAll(filterTask.getValue());
            
            if (isListView) {
                displayVehiclesInListAsync();
            } else {
                displayVehiclesInCardsAsync();
            }
            
            updateStatistics();
        });

        filterTask.setOnFailed(e -> {
            System.err.println("Filter task failed: " + filterTask.getException().getMessage());
        });

        Thread filterThread = new Thread(filterTask);
        filterThread.setDaemon(true);
        filterThread.start();
    }

    private boolean matchesFilters(Vehicule vehicule) {
        if (searchField != null && !searchField.getText().trim().isEmpty()) {
            String searchText = searchField.getText().toLowerCase().trim();
            String vehicleText = (vehicule.getMarque() + " " + vehicule.getModele() + " " + vehicule.getImmatriculation()).toLowerCase();
            if (!vehicleText.contains(searchText)) {
                return false;
            }
        }

        if (marqueFilter != null && marqueFilter.getValue() != null && !marqueFilter.getValue().isEmpty()) {
            if (!vehicule.getMarque().equalsIgnoreCase(marqueFilter.getValue())) {
                return false;
            }
        }

        if (etatFilter != null && etatFilter.getValue() != null && !etatFilter.getValue().isEmpty()) {
            String vehicleEtat = vehicule.getEtat();
            if (vehicleEtat == null || !vehicleEtat.equalsIgnoreCase(etatFilter.getValue())) {
                return false;
            }
        }

        if (carburantFilter != null && carburantFilter.getValue() != null && !carburantFilter.getValue().isEmpty()) {
            if (!vehicule.getCarburant().equalsIgnoreCase(carburantFilter.getValue())) {
                return false;
            }
        }

        if (prixMaxFilter != null && !prixMaxFilter.getText().trim().isEmpty()) {
            try {
                double maxPrice = Double.parseDouble(prixMaxFilter.getText().trim());
                Double vehiclePrice = vehicule.getPrixParJour();
                if (vehiclePrice != null && vehiclePrice > maxPrice) {
                    return false;
                }
            } catch (NumberFormatException e) {
                // Invalid price format, ignore filter
            }
        }

        return true;
    }

    @FXML
    private void handleClearFilters() {
        if (marqueFilter != null) {
            marqueFilter.getSelectionModel().clearSelection();
            marqueFilter.setValue(null);
        }
        if (etatFilter != null) {
            etatFilter.getSelectionModel().clearSelection();
            etatFilter.setValue(null);
        }
        if (carburantFilter != null) {
            carburantFilter.getSelectionModel().clearSelection();
            carburantFilter.setValue(null);
        }
        if (prixMaxFilter != null) {
            prixMaxFilter.clear();
        }
        if (searchField != null) {
            searchField.clear();
        }

        if (allVehicles != null) {
            filteredVehicles.setAll(allVehicles);
        }

        if (isListView) {
            displayVehiclesInListAsync();
        } else {
            displayVehiclesInCardsAsync();
        }

        updateStatistics();
    }

    @FXML
    private void handleExport() {
        if (filteredVehicles == null || filteredVehicles.isEmpty()) {
            showAlert("Export", "Aucun véhicule à exporter", Alert.AlertType.WARNING);
            return;
        }

        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Exporter la liste des véhicules");
            fileChooser.getExtensionFilters().addAll(
                    new FileChooser.ExtensionFilter("Fichiers CSV", "*.csv"),
                    new FileChooser.ExtensionFilter("Fichiers Excel", "*.xlsx"),
                    new FileChooser.ExtensionFilter("Tous les fichiers", "*.*")
            );

            File file = fileChooser.showSaveDialog(btnExport.getScene().getWindow());

            if (file != null) {
                exportToCSVAsync(file);
            }
        } catch (Exception e) {
            showAlert("Erreur d'Export",
                    "Une erreur s'est produite lors de l'export:\n" + e.getMessage(),
                    Alert.AlertType.ERROR);
        }
    }

    /**
     * Async CSV export to prevent UI blocking
     */
    private void exportToCSVAsync(File file) {
        Task<Void> exportTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
                    writer.println("ID,Marque,Modèle,Immatriculation,Carburant,État,Prix/Jour,Kilométrage");

                    for (Vehicule vehicule : filteredVehicles) {
                        writer.printf("%d,%s,%s,%s,%s,%s,%.2f,%d%n",
                                vehicule.getId(),
                                escapeCSV(vehicule.getMarque()),
                                escapeCSV(vehicule.getModele()),
                                escapeCSV(vehicule.getImmatriculation()),
                                escapeCSV(vehicule.getCarburant()),
                                escapeCSV(vehicule.getEtat()),
                                vehicule.getPrixParJour() != null ? vehicule.getPrixParJour() : 0.0,
                                vehicule.getMetrage() != null ? vehicule.getMetrage() : 0
                        );
                    }
                }
                return null;
            }
        };

        exportTask.setOnSucceeded(e -> {
            Platform.runLater(() -> {
                showAlert("Export Réussi",
                        "Liste des véhicules exportée avec succès!\n" +
                                "Fichier: " + file.getName() + "\n" +
                                "Véhicules exportés: " + filteredVehicles.size(),
                        Alert.AlertType.INFORMATION);
            });
        });

        exportTask.setOnFailed(e -> {
            Platform.runLater(() -> {
                showAlert("Erreur d'Export",
                        "Une erreur s'est produite lors de l'export:\n" + exportTask.getException().getMessage(),
                        Alert.AlertType.ERROR);
            });
        });

        Thread exportThread = new Thread(exportTask);
        exportThread.setDaemon(true);
        exportThread.start();
    }

    private String escapeCSV(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    @FXML
    private void handleAddVehicle() {
        try {
            Dialog<Vehicule> dialog = new Dialog<>();
            dialog.setTitle("Ajouter un Nouveau Véhicule");
            dialog.setHeaderText("Saisissez les informations du véhicule");

            GridPane grid = new GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new Insets(20, 150, 10, 10));

            TextField marqueField = new TextField();
            marqueField.setPromptText("Marque");
            TextField modeleField = new TextField();
            modeleField.setPromptText("Modèle");
            TextField immatriculationField = new TextField();
            immatriculationField.setPromptText("Immatriculation");
            TextField carburantField = new TextField();
            carburantField.setPromptText("Carburant");
            TextField prixField = new TextField();
            prixField.setPromptText("Prix par jour");
            TextField kilometrageField = new TextField();
            kilometrageField.setPromptText("Kilométrage");

            ComboBox<String> etatCombo = new ComboBox<>();
            etatCombo.getItems().addAll("disponible", "loué", "en panne", "maintenance");
            etatCombo.setValue("disponible");

            grid.add(new Label("Marque:"), 0, 0);
            grid.add(marqueField, 1, 0);
            grid.add(new Label("Modèle:"), 0, 1);
            grid.add(modeleField, 1, 1);
            grid.add(new Label("Immatriculation:"), 0, 2);
            grid.add(immatriculationField, 1, 2);
            grid.add(new Label("Carburant:"), 0, 3);
            grid.add(carburantField, 1, 3);
            grid.add(new Label("Prix/jour:"), 0, 4);
            grid.add(prixField, 1, 4);
            grid.add(new Label("Kilométrage:"), 0, 5);
            grid.add(kilometrageField, 1, 5);
            grid.add(new Label("État:"), 0, 6);
            grid.add(etatCombo, 1, 6);

            dialog.getDialogPane().setContent(grid);

            ButtonType saveButtonType = new ButtonType("Ajouter", ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);

            Node saveButton = dialog.getDialogPane().lookupButton(saveButtonType);
            saveButton.setDisable(true);

            marqueField.textProperty().addListener((observable, oldValue, newValue) -> {
                saveButton.setDisable(newValue.trim().isEmpty() || modeleField.getText().trim().isEmpty() ||
                        immatriculationField.getText().trim().isEmpty());
            });
            modeleField.textProperty().addListener((observable, oldValue, newValue) -> {
                saveButton.setDisable(newValue.trim().isEmpty() || marqueField.getText().trim().isEmpty() ||
                        immatriculationField.getText().trim().isEmpty());
            });
            immatriculationField.textProperty().addListener((observable, oldValue, newValue) -> {
                saveButton.setDisable(newValue.trim().isEmpty() || marqueField.getText().trim().isEmpty() ||
                        modeleField.getText().trim().isEmpty());
            });

            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == saveButtonType) {
                    try {
                        Vehicule vehicule = new Vehicule();
                        vehicule.setMarque(marqueField.getText().trim());
                        vehicule.setModele(modeleField.getText().trim());
                        vehicule.setImmatriculation(immatriculationField.getText().trim());
                        vehicule.setCarburant(carburantField.getText().trim());

                        String prixText = prixField.getText().trim();
                        if (!prixText.isEmpty()) {
                            vehicule.setPrixParJour(Double.parseDouble(prixText));
                        }

                        String kmText = kilometrageField.getText().trim();
                        if (!kmText.isEmpty()) {
                            vehicule.setMetrage(Integer.parseInt(kmText));
                        }

                        vehicule.setEtat(etatCombo.getValue());
                        return vehicule;
                    } catch (Exception e) {
                        showAlert("Erreur de Saisie", "Veuillez vérifier les données saisies:\n" + e.getMessage(), Alert.AlertType.ERROR);
                        return null;
                    }
                }
                return null;
            });

            Optional<Vehicule> result = dialog.showAndWait();
            result.ifPresent(vehicule -> {
                saveVehicleAsync(vehicule);
            });

        } catch (Exception e) {
            showAlert("Erreur", "Erreur lors de l'ouverture du formulaire:\n" + e.getMessage(), Alert.AlertType.ERROR);
        }
    }

    /**
     * Async vehicle save
     */
    private void saveVehicleAsync(Vehicule vehicule) {
        Task<Void> saveTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                vehiculeDAO.save(vehicule);
                return null;
            }
        };

        saveTask.setOnSucceeded(e -> {
            Platform.runLater(() -> {
                loadCatalogueAsync();
                showAlert("Succès", "Véhicule ajouté avec succès!", Alert.AlertType.INFORMATION);
            });
        });

        saveTask.setOnFailed(e -> {
            Platform.runLater(() -> {
                showAlert("Erreur", "Erreur lors de l'ajout du véhicule:\n" + saveTask.getException().getMessage(), Alert.AlertType.ERROR);
            });
        });

        Thread saveThread = new Thread(saveTask);
        saveThread.setDaemon(true);
        saveThread.start();
    }

    private void showAlert(String title, String message, Alert.AlertType type) {
        Platform.runLater(() -> {
            Alert alert = new Alert(type);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    @FXML
    public void initialize() {
        // Preload the placeholder image
        placeholderImage = createPlaceholderImage();

        // Initialize view mode
        isListView = false;

        // Initialize collections
        allVehicles = FXCollections.observableArrayList();
        filteredVehicles = FXCollections.observableArrayList();

        try {
            // Initialize filter options
            initializeFilterOptions();

            // Load catalogue data asynchronously
            loadCatalogueAsync();
        } catch (Exception e) {
            showAlert("Erreur d'Initialisation",
                    "Erreur lors de l'initialisation du catalogue:\n" + e.getMessage(),
                    Alert.AlertType.ERROR);
        }
    }

    private void initializeFilterOptions() {
        try {
            // Load filter options asynchronously
            CompletableFuture.supplyAsync(() -> vehiculeDAO.findAll())
                .thenAccept(vehicules -> {
                    Platform.runLater(() -> {
                        if (marqueFilter != null) {
                            List<String> marques = vehicules.stream()
                                    .map(Vehicule::getMarque)
                                    .filter(marque -> marque != null && !marque.isEmpty())
                                    .distinct()
                                    .sorted()
                                    .collect(Collectors.toList());
                            marqueFilter.setItems(FXCollections.observableArrayList(marques));
                        }

                        if (etatFilter != null) {
                            List<String> etats = vehicules.stream()
                                    .map(Vehicule::getEtat)
                                    .filter(etat -> etat != null && !etat.isEmpty())
                                    .distinct()
                                    .sorted()
                                    .collect(Collectors.toList());
                            etatFilter.setItems(FXCollections.observableArrayList(etats));
                        }

                        if (carburantFilter != null) {
                            List<String> carburants = vehicules.stream()
                                    .map(Vehicule::getCarburant)
                                    .filter(carburant -> carburant != null && !carburant.isEmpty())
                                    .distinct()
                                    .sorted()
                                    .collect(Collectors.toList());
                            carburantFilter.setItems(FXCollections.observableArrayList(carburants));
                        }
                    });
                })
                .exceptionally(throwable -> {
                    Platform.runLater(() -> {
                        System.err.println("Error initializing filter options: " + throwable.getMessage());
                    });
                    return null;
                });

        } catch (Exception e) {
            System.err.println("Error initializing filter options: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateStatistics(List<Vehicule> vehicules) {
        if (vehicules == null) return;

        long availableCount = vehicules.stream().filter(v -> "disponible".equalsIgnoreCase(v.getEtat())).count();
        long rentedCount = vehicules.stream().filter(v -> "loué".equalsIgnoreCase(v.getEtat()) || "loue".equalsIgnoreCase(v.getEtat())).count();
        long maintenanceCount = vehicules.stream().filter(v -> "en panne".equalsIgnoreCase(v.getEtat()) || "maintenance".equalsIgnoreCase(v.getEtat())).count();
        long totalCount = vehicules.size();

        if (lblAvailableCount != null) lblAvailableCount.setText(String.valueOf(availableCount));
        if (lblRentedCount != null) lblRentedCount.setText(String.valueOf(rentedCount));
        if (lblMaintenanceCount != null) lblMaintenanceCount.setText(String.valueOf(maintenanceCount));
        if (lblTotalVehicles != null) lblTotalVehicles.setText(String.valueOf(totalCount));
    }

    private void updateStatistics() {
        if (filteredVehicles != null) {
            updateStatistics(filteredVehicles);
        }
    }

    /**
     * Async catalogue loading
     */
    private void loadCatalogueAsync() {
        if (isLoading) {
            if (currentLoadTask != null) {
                currentLoadTask.cancel();
            }
        }

        isLoading = true;

        currentLoadTask = new Task<List<Vehicule>>() {
            @Override
            protected List<Vehicule> call() throws Exception {
                return vehiculeDAO.findAll();
            }
        };

        currentLoadTask.setOnSucceeded(e -> {
            Platform.runLater(() -> {
                List<Vehicule> vehicles = currentLoadTask.getValue();
                allVehicles.setAll(vehicles);
                filteredVehicles.setAll(vehicles);

                if (isListView) {
                    displayVehiclesInListAsync();
                } else {
                    displayVehiclesInCardsAsync();
                }

                updateStatistics();
                isLoading = false;
            });
        });

        currentLoadTask.setOnFailed(e -> {
            Platform.runLater(() -> {
                showAlert("Erreur", "Erreur lors du chargement du catalogue:\n" + currentLoadTask.getException().getMessage(), Alert.AlertType.ERROR);
                isLoading = false;
            });
        });

        Thread loadThread = new Thread(currentLoadTask);
        loadThread.setDaemon(true);
        loadThread.start();
    }

    /**
     * Async card display with virtual flow optimization
     */
    private void displayVehiclesInCardsAsync() {
        if (cataloguePane == null || filteredVehicles == null) return;

        Task<List<VBox>> cardTask = new Task<List<VBox>>() {
            @Override
            protected List<VBox> call() throws Exception {
                List<VBox> cards = new ArrayList<>();
                
                // Process in batches to prevent memory issues
                int batchSize = 20;
                for (int i = 0; i < filteredVehicles.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, filteredVehicles.size());
                    List<Vehicule> batch = filteredVehicles.subList(i, endIndex);
                    
                    for (Vehicule v : batch) {
                        if (isCancelled()) return cards;
                        VBox card = createModernVehicleCard(v);
                        cards.add(card);
                    }
                    
                    // Small delay to prevent UI freezing
                    if (i + batchSize < filteredVehicles.size()) {
                        Thread.sleep(10);
                    }
                }
                
                return cards;
            }
        };

        cardTask.setOnSucceeded(e -> {
            Platform.runLater(() -> {
                cataloguePane.getChildren().clear();
                cataloguePane.getChildren().addAll(cardTask.getValue());

                if (lblTotalCount != null) {
                    lblTotalCount.setText("Total: " + filteredVehicles.size() + " véhicules");
                }
            });
        });

        cardTask.setOnFailed(e -> {
            System.err.println("Card display task failed: " + cardTask.getException().getMessage());
        });

        Thread cardThread = new Thread(cardTask);
        cardThread.setDaemon(true);
        cardThread.start();
    }

    /**
     * Async list display
     */
    private void displayVehiclesInListAsync() {
        if (listViewPane == null || filteredVehicles == null) {
            displayVehiclesInCardsAsync();
            return;
        }

        Task<List<Node>> listTask = new Task<List<Node>>() {
            @Override
            protected List<Node> call() throws Exception {
                List<Node> nodes = new ArrayList<>();
                
                // Create header
                HBox header = new HBox(10);
                header.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");
                header.getChildren().addAll(
                        createListHeader("Marque", 120),
                        createListHeader("Modèle", 120),
                        createListHeader("Immatriculation", 120),
                        createListHeader("État", 100),
                        createListHeader("Prix/jour", 80),
                        createListHeader("Kilométrage", 100),
                        createListHeader("Actions", 150)
                );
                nodes.add(header);

                // Add vehicle rows in batches
                int batchSize = 25;
                for (int i = 0; i < filteredVehicles.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, filteredVehicles.size());
                    List<Vehicule> batch = filteredVehicles.subList(i, endIndex);
                    
                    for (Vehicule v : batch) {
                        if (isCancelled()) return nodes;
                        HBox row = createVehicleListRow(v);
                        nodes.add(row);
                    }
                    
                    if (i + batchSize < filteredVehicles.size()) {
                        Thread.sleep(5);
                    }
                }
                
                return nodes;
            }
        };

        listTask.setOnSucceeded(e -> {
            Platform.runLater(() -> {
                listViewPane.getChildren().clear();
                listViewPane.getChildren().addAll(listTask.getValue());

                if (lblTotalCount != null) {
                    lblTotalCount.setText("Total: " + filteredVehicles.size() + " véhicules");
                }
            });
        });

        listTask.setOnFailed(e -> {
            System.err.println("List display task failed: " + listTask.getException().getMessage());
        });

        Thread listThread = new Thread(listTask);
        listThread.setDaemon(true);
        listThread.start();
    }

    private Label createListHeader(String text, double width) {
        Label header = new Label(text);
        header.setMinWidth(width);
        header.setMaxWidth(width);
        header.setStyle("-fx-font-weight: bold; -fx-text-fill: #495057;");
        return header;
    }

    private HBox createVehicleListRow(Vehicule v) {
        HBox row = new HBox(10);
        row.getStyleClass().add(LIST_ROW_STYLE);

        // Add hover effect
        row.setOnMouseEntered(e -> row.getStyleClass().add(LIST_ROW_HOVER_STYLE));
        row.setOnMouseExited(e -> row.getStyleClass().remove(LIST_ROW_HOVER_STYLE));

        // Create cells
        Label marqueLabel = createListCell(v.getMarque(), 120);
        Label modeleLabel = createListCell(v.getModele(), 120);
        Label immatriculationLabel = createListCell(v.getImmatriculation(), 120);
        Label etatLabel = createListCell(v.getEtat(), 100);
        Label prixLabel = createListCell(v.getPrixParJour() != null ? String.format("%.2f €", v.getPrixParJour()) : "N/A", 80);
        Label kilometrageLabel = createListCell(v.getMetrage() != null ? String.valueOf(v.getMetrage()) + " km" : "N/A", 100);

        // Action buttons
        HBox actions = new HBox(5);
        actions.setMinWidth(150);
        actions.setMaxWidth(150);
        actions.setAlignment(Pos.CENTER_LEFT);

        Button detailBtn = new Button("👁️");
        detailBtn.getStyleClass().add(DETAIL_BUTTON_STYLE);
        detailBtn.setOnAction(e -> showVehiculeDetail(v));

        Button editBtn = new Button("✏️");
        editBtn.getStyleClass().add(DETAIL_BUTTON_STYLE);
        editBtn.setOnAction(e -> editVehicle(v));

        actions.getChildren().addAll(detailBtn, editBtn);

        row.getChildren().addAll(marqueLabel, modeleLabel, immatriculationLabel, etatLabel, prixLabel, kilometrageLabel, actions);

        return row;
    }

    private Label createListCell(String text, double width) {
        Label cell = new Label(text != null ? text : "");
        cell.setMinWidth(width);
        cell.setMaxWidth(width);
        cell.setStyle("-fx-text-fill: #212529;");
        return cell;
    }

    private void editVehicle(Vehicule v) {
        showAlert("Modifier Véhicule", "Fonctionnalité de modification à implémenter", Alert.AlertType.INFORMATION);
    }

    private VBox createModernVehicleCard(Vehicule v) {
        VBox card = new VBox();
        card.getStyleClass().add(CARD_STYLE);
        card.setPrefWidth(320);
        card.setMaxWidth(320);

        // Vehicle image container
        StackPane imageContainer = new StackPane();
        imageContainer.getStyleClass().add("image-container");
        imageContainer.setAlignment(Pos.CENTER);

        ImageView carImage = new ImageView();
        carImage.setFitHeight(160);
        carImage.setFitWidth(280);
        carImage.setPreserveRatio(true);
        carImage.getStyleClass().add("vehicle-image");

        // Load image with caching
        loadImageAsync(v.getPhotoUrl(), carImage);

        // Add status badge on top of image
        Label statusBadge = new Label(v.getEtat() != null ? v.getEtat() : "N/A");
        statusBadge.getStyleClass().add(STATUS_BADGE_STYLE);
        statusBadge.setStyle(statusBadge.getStyle() + "-fx-background-color: " + getStatusBackgroundColor(v.getEtat()) + "; " +
                "-fx-text-fill: " + getStatusTextColor(v.getEtat()) + ";");
        StackPane.setAlignment(statusBadge, Pos.TOP_RIGHT);
        StackPane.setMargin(statusBadge, new Insets(12));

        imageContainer.getChildren().addAll(carImage, statusBadge);

        // Vehicle info container
        VBox infoContainer = new VBox(16);
        infoContainer.setStyle("-fx-padding: 20;");

        // Title and price
        HBox titleRow = new HBox();
        titleRow.setAlignment(Pos.CENTER_LEFT);

        Label titleLabel = new Label((v.getMarque() != null ? v.getMarque() : "") + " " +
                (v.getModele() != null ? v.getModele() : ""));
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1e293b;");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        Label priceLabel = new Label((v.getPrixParJour() != null ? v.getPrixParJour() : 0) + " DH/jour");
        priceLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #10b981;");

        titleRow.getChildren().addAll(titleLabel, spacer, priceLabel);

        // Details grid
        GridPane detailsGrid = new GridPane();
        detailsGrid.setHgap(12);
        detailsGrid.setVgap(8);
        detailsGrid.setStyle("-fx-padding: 8 0;");

        // Registration
        Label immatIcon = new Label("🔖");
        Label immatLabel = new Label(v.getImmatriculation() != null ? v.getImmatriculation() : "N/A");
        immatLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        detailsGrid.addRow(0, immatIcon, immatLabel);

        // Fuel
        Label carburantIcon = new Label("⛽");
        Label carburantLabel = new Label(v.getCarburant() != null ? v.getCarburant() : "N/A");
        carburantLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        detailsGrid.addRow(1, carburantIcon, carburantLabel);

        // Mileage
        Label kmIcon = new Label("📏");
        Label kmLabel = new Label(v.getMetrage() != null ? String.valueOf(v.getMetrage()) + " km" : "N/A");
        kmLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        detailsGrid.addRow(2, kmIcon, kmLabel);

        // Action buttons
        HBox actionButtons = new HBox(12);
        actionButtons.setAlignment(Pos.CENTER);

        Button btnLouer = new Button("Louer");
        btnLouer.getStyleClass().add(RENT_BUTTON_STYLE);
        btnLouer.setOnAction(e -> openLocationCreateWithVehicle(v));

        Button btnDetails = new Button("Détails");
        btnDetails.getStyleClass().add(DETAIL_BUTTON_STYLE);
        btnDetails.setOnAction(e -> showVehiculeDetail(v));

        actionButtons.getChildren().addAll(btnDetails, btnLouer);

        infoContainer.getChildren().addAll(titleRow, detailsGrid, actionButtons);
        card.getChildren().addAll(imageContainer, infoContainer);

        // Add hover effect
        card.setOnMouseEntered(e -> card.getStyleClass().add(CARD_HOVER_STYLE));
        card.setOnMouseExited(e -> card.getStyleClass().remove(CARD_HOVER_STYLE));

        // Register card for memory management
        MemoryManager.registerObject("vehicle_card_" + v.getId(), card);

        return card;
    }

    /**
     * Async image loading with caching
     */
    private void loadImageAsync(String photoUrl, ImageView imageView) {
        if (photoUrl != null && !photoUrl.isEmpty()) {
            // Check cache first
            Image cachedImage = imageCache.get(photoUrl);
            if (cachedImage != null) {
                imageView.setImage(cachedImage);
                return;
            }

            // Load asynchronously
            Task<Image> imageTask = new Task<Image>() {
                @Override
                protected Image call() throws Exception {
                    return new Image(photoUrl, true); // Background loading
                }
            };

            imageTask.setOnSucceeded(e -> {
                Image loadedImage = imageTask.getValue();
                imageCache.put(photoUrl, loadedImage);
                Platform.runLater(() -> imageView.setImage(loadedImage));
            });

            imageTask.setOnFailed(e -> {
                Platform.runLater(() -> imageView.setImage(placeholderImage));
            });

            Thread imageThread = new Thread(imageTask);
            imageThread.setDaemon(true);
            imageThread.start();
        } else {
            imageView.setImage(placeholderImage);
        }
    }

    private Image createPlaceholderImage() {
        try {
            return new Image("/default-car.png");
        } catch (Exception e) {
            WritableImage wi = new WritableImage(280, 160);
            PixelWriter pw = wi.getPixelWriter();
            for (int x = 0; x < 280; x++) {
                for (int y = 0; y < 160; y++) {
                    pw.setColor(x, y, Color.LIGHTGRAY);
                }
            }
            return wi;
        }
    }

    private String getStatusBackgroundColor(String etat) {
        if (etat == null) return "#f3f4f6";
        switch (etat.toLowerCase()) {
            case "disponible": return "#dcfce7";
            case "loué": return "#fef3c7";
            case "en panne": return "#fee2e2";
            default: return "#f3f4f6";
        }
    }

    private String getStatusTextColor(String etat) {
        if (etat == null) return "#6b7280";
        switch (etat.toLowerCase()) {
            case "disponible": return "#166534";
            case "loué": return "#92400e";
            case "en panne": return "#991b1b";
            default: return "#6b7280";
        }
    }

    private void openLocationCreateWithVehicle(Vehicule v) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location_create.fxml"));
            javafx.scene.Parent root = loader.load();

            controller.LocationCreateController controller = loader.getController();
            if (controller != null) {
                controller.setVehicule(v);
            }

            javafx.scene.Scene scene = cataloguePane.getScene();
            if (scene != null) {
                javafx.scene.Node contentPane = scene.lookup("#contentPane");
                if (contentPane instanceof javafx.scene.layout.StackPane) {
                    ((javafx.scene.layout.StackPane) contentPane).getChildren().setAll(root);
                } else {
                    javafx.stage.Stage stage = new javafx.stage.Stage();
                    stage.setTitle("Nouvelle Location - " + v.getMarque() + " " + v.getModele());
                    stage.setScene(new javafx.scene.Scene(root));
                    stage.setMaximized(true);
                    stage.show();
                }
            }
        } catch (Exception e) {
            System.err.println("Error opening location create: " + e.getMessage());
            e.printStackTrace();

            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Erreur lors de l'ouverture du formulaire de location");
            alert.setContentText("Impossible d'ouvrir le formulaire de location pour ce véhicule: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private void showVehiculeDetail(Vehicule v) {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Détails du Véhicule");
        dialog.getDialogPane().setStyle("-fx-background-color: rgba(241, 245, 249, 0.9);");

        VBox content = new VBox(20);
        content.setStyle("-fx-padding: 32; -fx-background-color: white; -fx-background-radius: 16; " +
                "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 3);");

        HBox headerBox = new HBox(12);
        headerBox.setAlignment(Pos.CENTER_LEFT);

        Label header = new Label(v.getMarque() + " " + v.getModele());
        header.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1e293b;");

        Label statusBadge = new Label(v.getEtat() != null ? v.getEtat() : "N/A");
        statusBadge.setStyle("-fx-background-color: " + getStatusBackgroundColor(v.getEtat()) + "; " +
                "-fx-text-fill: " + getStatusTextColor(v.getEtat()) + "; " +
                "-fx-background-radius: 12; " +
                "-fx-padding: 4 12; " +
                "-fx-font-size: 12px; " +
                "-fx-font-weight: bold;");

        headerBox.getChildren().addAll(header, statusBadge);

        StackPane imageContainer = new StackPane();
        imageContainer.setStyle("-fx-background-color: #f8fafc; -fx-background-radius: 12;");

        ImageView img = new ImageView();
        img.setFitWidth(400);
        img.setFitHeight(225);
        img.setStyle("-fx-background-radius: 8;");
        loadImageAsync(v.getPhotoUrl(), img);
        imageContainer.getChildren().add(img);

        GridPane detailsGrid = new GridPane();
        detailsGrid.setHgap(24);
        detailsGrid.setVgap(12);
        detailsGrid.setStyle("-fx-padding: 16 0;");

        addDetailRow(detailsGrid, 0, "🔖 Immatriculation:", v.getImmatriculation());
        addDetailRow(detailsGrid, 1, "⛽ Carburant:", v.getCarburant());
        addDetailRow(detailsGrid, 2, "📏 Métrage:", v.getMetrage() != null ? String.valueOf(v.getMetrage()) + " km" : "N/A");
        addDetailRow(detailsGrid, 3, "📅 Date acquisition:", v.getDateAcquisition() != null ? v.getDateAcquisition().toString() : "N/A");
        addDetailRow(detailsGrid, 0, "💰 Prix/jour:", v.getPrixParJour() != null ? String.format("%.2f DH", v.getPrixParJour()) : "N/A");
        addDetailRow(detailsGrid, 1, "🏎️ Chevaux:", v.getNbreChevaux() != null ? v.getNbreChevaux().toString() : "N/A");
        addDetailRow(detailsGrid, 2, "🔄 Dernière utilisation:", v.getLastUsed() != null ? v.getLastUsed().toString() : "N/A");
        addDetailRow(detailsGrid, 3, "🛡️ Assurance:",
                (v.getAssuranceCompagnie() != null ? v.getAssuranceCompagnie() : "") +
                        (v.getAssuranceExpiration() != null ? " (exp: " + v.getAssuranceExpiration() + ")" : "") +
                        (v.getAssuranceNumero() != null ? " #" + v.getAssuranceNumero() : ""));

        HBox actions = new HBox(16);
        actions.setAlignment(Pos.CENTER);
        actions.setStyle("-fx-padding: 16 0 0 0;");

        Button btnReserver = createActionButton("Louer", "#3b82f6", e -> {
            dialog.close();
            showReservationDialog(v);
        });

        Button btnHistorique = createActionButton("Historique", "#64748b", e -> {
            dialog.close();
            showHistoriqueElaborate(v);
        });

        Button btnDisponibilite = createActionButton("Disponibilité", "#10b981", e -> {
            dialog.close();
            showDisponibiliteCalendar(v);
        });

        actions.getChildren().addAll(btnReserver, btnHistorique, btnDisponibilite);
        content.getChildren().addAll(headerBox, imageContainer, detailsGrid, actions);

        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);

        Node closeButton = dialog.getDialogPane().lookupButton(ButtonType.CLOSE);
        closeButton.setVisible(false);

        FadeTransition ft = new FadeTransition(Duration.millis(200), content);
        ft.setFromValue(0);
        ft.setToValue(1);
        ft.play();

        dialog.showAndWait();
    }

    private void addDetailRow(GridPane grid, int row, String label, String value) {
        Label detailLabel = new Label(label);
        detailLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-weight: bold;");

        Label detailValue = new Label(value != null ? value : "N/A");
        detailValue.setStyle("-fx-font-size: 14px; -fx-text-fill: #334155;");
        detailValue.setWrapText(true);

        grid.addRow(row, detailLabel, detailValue);
    }

    private Button createActionButton(String text, String color, EventHandler<ActionEvent> handler) {
        Button btn = new Button(text);
        btn.setStyle("-fx-background-color: " + color + "; " +
                "-fx-text-fill: white; " +
                "-fx-font-size: 14px; " +
                "-fx-font-weight: bold; " +
                "-fx-background-radius: 8; " +
                "-fx-padding: 12 24; " +
                "-fx-cursor: hand;");
        btn.setOnAction(handler);

        btn.setOnMouseEntered(e -> btn.setStyle(btn.getStyle() + "-fx-effect: dropshadow(gaussian, " + color + ", 10, 0.3, 0, 1);"));
        btn.setOnMouseExited(e -> btn.setStyle(btn.getStyle().replace("-fx-effect: dropshadow(gaussian, " + color + ", 10, 0.3, 0, 1);", "")));

        return btn;
    }

    private void showReservationDialog(Vehicule v) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location_create.fxml"));
            javafx.scene.Parent root = loader.load();
            controller.LocationCreateController controller = loader.getController();
            controller.setVehicule(v);
            javafx.scene.Scene scene = cataloguePane.getScene();
            javafx.scene.Node node = scene.lookup("#contentPane");
            if (node instanceof javafx.scene.layout.StackPane contentPane) {
                contentPane.getChildren().setAll(root);
            } else {
                javafx.stage.Stage stage = (javafx.stage.Stage) cataloguePane.getScene().getWindow();
                stage.setScene(new javafx.scene.Scene(root));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showDisponibiliteCalendar(Vehicule v) {
        Platform.runLater(() -> {
            try {
                String fxmlPath = "/view/disponibilite.fxml";
                java.net.URL fxmlUrl = getClass().getResource(fxmlPath);
                if (fxmlUrl == null) {
                    throw new RuntimeException("FXML file not found: " + fxmlPath);
                }

                FXMLLoader loader = new FXMLLoader(fxmlUrl);
                javafx.scene.Parent root = loader.load();

                if (root == null) {
                    throw new RuntimeException("Root is null after loading");
                }

                DisponibiliteController ctrl = loader.getController();

                javafx.stage.Stage dialog = new javafx.stage.Stage();
                dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);

                javafx.scene.Scene scene = new javafx.scene.Scene(root, 1000, 700);
                dialog.setScene(scene);
                dialog.setTitle("Calendrier de Disponibilité - " + v.getMarque() + " " + v.getModele());

                if (ctrl != null) {
                    ctrl.setVehicule(v);
                }

                javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
                javafx.geometry.Rectangle2D visualBounds = screen.getVisualBounds();
                dialog.setX(visualBounds.getMinX());
                dialog.setY(visualBounds.getMinY());
                dialog.setWidth(visualBounds.getWidth());
                dialog.setHeight(visualBounds.getHeight() * 0.95);

                dialog.setResizable(true);
                dialog.centerOnScreen();
                dialog.show();
                dialog.toFront();
                dialog.requestFocus();

            } catch (Exception e) {
                System.err.println("ERROR loading disponibilite calendar: " + e.getMessage());
                e.printStackTrace();

                Platform.runLater(() -> {
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("Erreur");
                    alert.setHeaderText("Erreur de chargement");
                    alert.setContentText("Impossible de charger le calendrier de disponibilité: " + e.getMessage());
                    alert.showAndWait();
                });
            }
        });
    }

    private void showHistoriqueElaborate(Vehicule v) {
        try {
            FXMLUtil.createMaximizedWindow("/view/historique_locations_enhanced.fxml",
                    "📊 Historique Détaillé - " + v.getMarque() + " " + v.getModele(),
                    getClass());
        } catch (Exception e) {
            showAlert("Erreur", "Impossible de charger l'historique: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }
}

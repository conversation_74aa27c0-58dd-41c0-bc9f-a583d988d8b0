package controller;

import javafx.event.ActionEvent;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.layout.StackPane;
import javafx.stage.Screen;
import javafx.stage.Stage;
import javafx.geometry.Rectangle2D;
import util.FXMLUtil;
import java.io.IOException;

/**
 * Centralized Navigation Controller
 * Handles all navigation logic across the application
 */
public class NavigationController {
    
    // Navigation routes mapping
    public enum NavigationRoute {
        DASHBOARD("dashboard", "/view/dashboard_content.fxml", "📊 Tableau de Bord"),
        CLIENTS("clients", "/view/client.fxml", "👥 Gestion des Clients"),
        VEHICULES("vehicules", "/view/vehicule.fxml", "🚗 Gestion des Véhicules"),
        LOCATIONS("locations", "/view/location.fxml", "📅 Gestion des Locations"),
        PAIEMENTS("paiements", "/view/paiement.fxml", "💰 Gestion des Paiements"),
        CATALOGUE("catalogue", "/view/catalogue.fxml", "📋 Catalogue des Véhicules"),
        NEW_LOCATION("newLocation", "/view/location_create.fxml", "➕ Nouvelle Location"),
        USER_MANAGEMENT("userManagement", "/view/user_management.fxml", "⚙️ Gestion des Utilisateurs"),
        MAINTENANCE("maintenance", "/view/vehicle_maintenance.fxml", "🔧 Maintenance des Véhicules"),
        RETURN_PREVIEW("returnPreview", "/view/vehicle_return_preview.fxml", "📋 Prévisions de Retour"),
        RENTAL_HISTORY("rentalHistory", "/view/historique_locations_enhanced.fxml", "📊 Historique des Locations"),
        PAYMENT_HISTORY("paymentHistory", "/view/historique_paiements_enhanced.fxml", "💳 Historique des Paiements");
        
        private final String key;
        private final String fxmlPath;
        private final String title;
        
        NavigationRoute(String key, String fxmlPath, String title) {
            this.key = key;
            this.fxmlPath = fxmlPath;
            this.title = title;
        }
        
        public String getKey() { return key; }
        public String getFxmlPath() { return fxmlPath; }
        public String getTitle() { return title; }
        
        public static NavigationRoute fromKey(String key) {
            for (NavigationRoute route : values()) {
                if (route.key.equals(key)) {
                    return route;
                }
            }
            return null;
        }
    }
    
    /**
     * Handle navigation from button events
     */
    public static void handleNavigation(ActionEvent event, StackPane contentPane) {
        if (!(event.getSource() instanceof Button)) return;
        
        Button btn = (Button) event.getSource();
        String target = getNavigationTarget(btn);
        
        if (target != null) {
            navigateTo(target, contentPane);
        }
    }
    
    /**
     * Handle navigation from button events with stage context
     */
    public static void handleNavigation(ActionEvent event, Stage stage) {
        if (!(event.getSource() instanceof Button)) return;
        
        Button btn = (Button) event.getSource();
        String target = getNavigationTarget(btn);
        
        if (target != null) {
            navigateToNewWindow(target, stage);
        }
    }
    
    /**
     * Get navigation target from button
     */
    private static String getNavigationTarget(Button btn) {
        // Check button ID first
        String target = btn.getId();
        if (target != null) {
            // Remove "btn" prefix if present
            if (target.startsWith("btn")) {
                target = target.substring(3);
                target = target.substring(0, 1).toLowerCase() + target.substring(1);
            }
        }
        
        // Check userData if ID is null or doesn't match
        if (target == null || NavigationRoute.fromKey(target) == null) {
            target = (String) btn.getUserData();
        }
        
        return target;
    }
    
    /**
     * Navigate to a route within content pane
     */
    public static void navigateTo(String routeKey, StackPane contentPane) {
        NavigationRoute route = NavigationRoute.fromKey(routeKey);
        if (route == null) {
            System.err.println("Unknown navigation route: " + routeKey);
            return;
        }
        
        try {
            // Special handling for enhanced views that should open in new windows
            if (route == NavigationRoute.RENTAL_HISTORY || 
                route == NavigationRoute.PAYMENT_HISTORY ||
                route == NavigationRoute.MAINTENANCE ||
                route == NavigationRoute.RETURN_PREVIEW) {
                
                FXMLUtil.createMaximizedWindow(route.getFxmlPath(), route.getTitle(), NavigationController.class);
                return;
            }
            
            // Load view in content pane
            FXMLLoader loader = new FXMLLoader(NavigationController.class.getResource(route.getFxmlPath()));
            Node content = loader.load();
            contentPane.getChildren().setAll(content);
            
        } catch (IOException e) {
            System.err.println("Error loading view: " + route.getFxmlPath());
            e.printStackTrace();
            FXMLUtil.showError("Erreur de navigation: " + e.getMessage());
        }
    }
    
    /**
     * Navigate to a route in a new window
     */
    public static void navigateToNewWindow(String routeKey, Stage parentStage) {
        NavigationRoute route = NavigationRoute.fromKey(routeKey);
        if (route == null) {
            System.err.println("Unknown navigation route: " + routeKey);
            return;
        }
        
        try {
            FXMLUtil.createMaximizedWindow(route.getFxmlPath(), route.getTitle(), NavigationController.class);
        } catch (Exception e) {
            System.err.println("Error opening new window: " + route.getFxmlPath());
            e.printStackTrace();
            FXMLUtil.showError("Erreur de navigation: " + e.getMessage());
        }
    }
    
    /**
     * Direct navigation methods for programmatic use
     */
    public static void showDashboard(StackPane contentPane) {
        navigateTo("dashboard", contentPane);
    }
    
    public static void showClients(StackPane contentPane) {
        navigateTo("clients", contentPane);
    }
    
    public static void showVehicules(StackPane contentPane) {
        navigateTo("vehicules", contentPane);
    }
    
    public static void showLocations(StackPane contentPane) {
        navigateTo("locations", contentPane);
    }
    
    public static void showPaiements(StackPane contentPane) {
        navigateTo("paiements", contentPane);
    }
    
    public static void showCatalogue(StackPane contentPane) {
        navigateTo("catalogue", contentPane);
    }
    
    public static void showNewLocation(StackPane contentPane) {
        navigateTo("newLocation", contentPane);
    }
    
    public static void showUserManagement(StackPane contentPane) {
        navigateTo("userManagement", contentPane);
    }
    
    public static void showMaintenance() {
        try {
            FXMLUtil.createMaximizedWindow("/view/vehicle_maintenance.fxml", "🔧 Maintenance des Véhicules", NavigationController.class);
        } catch (Exception e) {
            FXMLUtil.showError("Erreur: " + e.getMessage());
        }
    }
    
    public static void showReturnPreview() {
        try {
            FXMLUtil.createMaximizedWindow("/view/vehicle_return_preview.fxml", "📋 Prévisions de Retour", NavigationController.class);
        } catch (Exception e) {
            FXMLUtil.showError("Erreur: " + e.getMessage());
        }
    }
    
    public static void showRentalHistory() {
        try {
            FXMLUtil.createMaximizedWindow("/view/historique_locations_enhanced.fxml", "📊 Historique des Locations", NavigationController.class);
        } catch (Exception e) {
            FXMLUtil.showError("Erreur: " + e.getMessage());
        }
    }
    
    public static void showPaymentHistory() {
        try {
            FXMLUtil.createMaximizedWindow("/view/historique_paiements_enhanced.fxml", "💳 Historique des Paiements", NavigationController.class);
        } catch (Exception e) {
            FXMLUtil.showError("Erreur: " + e.getMessage());
        }
    }
    
    /**
     * Get current route title for UI updates
     */
    public static String getRouteTitle(String routeKey) {
        NavigationRoute route = NavigationRoute.fromKey(routeKey);
        return route != null ? route.getTitle() : "Application";
    }
}

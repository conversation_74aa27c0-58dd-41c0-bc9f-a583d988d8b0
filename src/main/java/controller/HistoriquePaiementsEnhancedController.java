package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.control.cell.CheckBoxTableCell;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.geometry.Pos;
import javafx.stage.Stage;
import javafx.application.Platform;
import model.*;
import dao.*;
import util.FXMLUtil;

import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class HistoriquePaiementsEnhancedController implements Initializable {

    // Filter Controls
    @FXML private ComboBox<String> filterStatusCombo;
    @FXML private ComboBox<String> filterMethodCombo;
    @FXML private ComboBox<Client> filterClientCombo;
    @FXML private ComboBox<Vehicule> filterVehicleCombo;
    @FXML private DatePicker filterDateFrom;
    @FXML private DatePicker filterDateTo;
    @FXML private TextField filterAmountMin;
    @FXML private TextField filterAmountMax;
    @FXML private CheckBox filterOverdueOnly;
    @FXML private CheckBox filterPartialOnly;
    @FXML private TextField searchField;

    // Statistics Labels
    @FXML private Label lblTotalRevenue;
    @FXML private Label lblRevenueChange;
    @FXML private Label lblPaidCount;
    @FXML private Label lblPaidPercentage;
    @FXML private Label lblPendingCount;
    @FXML private Label lblPendingAmount;
    @FXML private Label lblOverdueCount;
    @FXML private Label lblOverdueAmount;

    // Charts and Analytics
    @FXML private VBox paymentMethodsChart;
    @FXML private VBox monthlyTrendsChart;

    // Table and Controls
    @FXML private TableView<PaymentRow> paymentsTable;
    @FXML private TableColumn<PaymentRow, Boolean> colSelect;
    @FXML private TableColumn<PaymentRow, Long> colId;
    @FXML private TableColumn<PaymentRow, String> colClient;
    @FXML private TableColumn<PaymentRow, String> colVehicle;
    @FXML private TableColumn<PaymentRow, Double> colAmount;
    @FXML private TableColumn<PaymentRow, String> colMethod;
    @FXML private TableColumn<PaymentRow, String> colDate;
    @FXML private TableColumn<PaymentRow, String> colStatus;
    @FXML private TableColumn<PaymentRow, String> colDueDate;
    @FXML private TableColumn<PaymentRow, String> colReference;
    @FXML private TableColumn<PaymentRow, Void> colActions;

    // Sidebar
    @FXML private VBox paymentDetailCard;
    @FXML private Label lblSelectedPayment;
    @FXML private VBox paymentDetails;
    @FXML private VBox paymentTimeline;

    // Bottom Statistics
    @FXML private Label lblTotalCount;
    @FXML private Label lblSelectedCount;
    @FXML private Label lblFilteredRevenue;
    @FXML private Label lblLastUpdate;

    // Action Buttons
    @FXML private Button btnRefresh;
    @FXML private Button btnClearFilters;
    @FXML private Button btnBulkActions;
    @FXML private Button btnMarkPaid;
    @FXML private Button btnSendReminder;
    @FXML private Button btnViewContract;
    @FXML private Button btnGenerateInvoice;
    @FXML private Button btnReconcile;
    @FXML private Button btnAnalytics;

    // Menu Items
    @FXML private MenuItem exportPaymentsCSV;
    @FXML private MenuItem exportPaymentsExcel;
    @FXML private MenuItem exportPaymentsPDF;
    @FXML private MenuItem exportUnpaid;
    @FXML private MenuItem exportOverdue;

    private PaiementDAO paiementDAO;
    private LocationDAO locationDAO;
    private ClientDAO clientDAO;
    private VehiculeDAO vehiculeDAO;
    private ObservableList<PaymentRow> allPayments;
    private ObservableList<PaymentRow> filteredPayments;
    private PaymentRow selectedPayment;

    // Inner class for table rows with selection
    public static class PaymentRow {
        private final BooleanProperty selected = new SimpleBooleanProperty(false);
        private final Paiement payment;
        private final Location location;
        private final Client client;
        private final Vehicule vehicule;

        public PaymentRow(Paiement payment) {
            this.payment = payment;
            this.location = payment.getLocation();
            this.client = location != null ? location.getClient() : null;
            this.vehicule = location != null ? location.getVehicule() : null;
        }

        // Getters
        public BooleanProperty selectedProperty() { return selected; }
        public boolean isSelected() { return selected.get(); }
        public void setSelected(boolean selected) { this.selected.set(selected); }
        public Paiement getPayment() { return payment; }
        public Location getLocation() { return location; }
        public Client getClient() { return client; }
        public Vehicule getVehicule() { return vehicule; }

        // Table column getters
        public Long getId() { return payment.getId(); }
        public String getClientName() { 
            return client != null ? client.getPrenom() + " " + client.getNom() : "N/A"; 
        }
        public String getVehicleName() { 
            return vehicule != null ? vehicule.getMarque() + " " + vehicule.getModele() : "N/A"; 
        }
        public Double getAmount() { return payment.getMontant(); }
        public String getMethod() {
            return payment.getMethodePaiement() != null ? payment.getMethodePaiement() : "N/A";
        }
        public String getDate() {
            return payment.getDatePaiement() != null ?
                payment.getDatePaiement().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A";
        }
        public String getStatus() {
            return payment.getStatut() != null ? payment.getStatut() : "En attente";
        }
        public String getDueDate() {
            LocalDate dueDate = payment.getDateEcheance();
            return dueDate != null ? dueDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A";
        }
        public String getReference() {
            return payment.getReference();
        }

        // Helper methods for enum-based access
        public Paiement.Status getStatusEnum() {
            return payment.getStatus();
        }

        public Paiement.MethodePaiement getMethodEnum() {
            return payment.getMethodePaiementEnum();
        }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        paiementDAO = new PaiementDAO();
        locationDAO = new LocationDAO();
        clientDAO = new ClientDAO();
        vehiculeDAO = new VehiculeDAO();

        setupTableColumns();
        setupFilterControls();
        loadData();
        updateStatistics();
        updateLastUpdateTime();
    }

    private void setupTableColumns() {
        // Selection column
        colSelect.setCellValueFactory(cellData -> cellData.getValue().selectedProperty());
        colSelect.setCellFactory(CheckBoxTableCell.forTableColumn(colSelect));
        colSelect.setEditable(true);

        // Data columns
        colId.setCellValueFactory(cellData -> new SimpleObjectProperty<>(cellData.getValue().getId()));
        colClient.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getClientName()));
        colVehicle.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getVehicleName()));
        colAmount.setCellValueFactory(cellData -> new SimpleObjectProperty<>(cellData.getValue().getAmount()));
        colMethod.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getMethod()));
        colDate.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getDate()));
        colStatus.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getStatus()));
        colDueDate.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getDueDate()));
        colReference.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getReference()));

        // Format amount column
        colAmount.setCellFactory(column -> new TableCell<PaymentRow, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f DH", item));
                }
            }
        });

        // Actions column
        colActions.setCellFactory(column -> new TableCell<PaymentRow, Void>() {
            private final Button viewBtn = new Button("👁");
            private final Button editBtn = new Button("✏");
            private final HBox buttons = new HBox(5, viewBtn, editBtn);

            {
                buttons.setAlignment(Pos.CENTER);
                viewBtn.setOnAction(e -> {
                    PaymentRow row = getTableView().getItems().get(getIndex());
                    showPaymentDetails(row);
                });
                editBtn.setOnAction(e -> {
                    PaymentRow row = getTableView().getItems().get(getIndex());
                    editPayment(row);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                setGraphic(empty ? null : buttons);
            }
        });

        // Optimized table selection listener
        paymentsTable.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null && newSelection != selectedPayment) {
                    selectedPayment = newSelection;
                    Platform.runLater(() -> showPaymentDetails(newSelection));
                }
            }
        );

        // Enable row selection with click
        paymentsTable.setRowFactory(tv -> {
            TableRow<PaymentRow> row = new TableRow<>();
            row.setOnMouseClicked(event -> {
                if (!row.isEmpty() && event.getClickCount() == 1) {
                    PaymentRow rowData = row.getItem();
                    if (rowData != null) {
                        selectedPayment = rowData;
                        paymentsTable.getSelectionModel().select(rowData);
                        showPaymentDetails(rowData);
                    }
                }
            });
            return row;
        });

        paymentsTable.setEditable(true);
    }



    private void setupFilterControls() {
        try {
            // Status filter - using enum display names
            List<String> statusOptions = new ArrayList<>();
            statusOptions.add("Tous");
            for (Paiement.Status status : Paiement.Status.values()) {
                statusOptions.add(status.getDisplayName());
            }
            filterStatusCombo.setItems(FXCollections.observableArrayList(statusOptions));
            filterStatusCombo.setValue("Tous");

            // Method filter - using enum display names
            List<String> methodOptions = new ArrayList<>();
            methodOptions.add("Toutes");
            for (Paiement.MethodePaiement method : Paiement.MethodePaiement.values()) {
                methodOptions.add(method.getDisplayName());
            }
            filterMethodCombo.setItems(FXCollections.observableArrayList(methodOptions));
            filterMethodCombo.setValue("Toutes");

            // Load clients and vehicles with error handling
            try {
                List<Client> clients = clientDAO.findAll();
                filterClientCombo.setItems(FXCollections.observableArrayList(clients));

                // Set up client display
                filterClientCombo.setCellFactory(param -> new ListCell<Client>() {
                    @Override
                    protected void updateItem(Client item, boolean empty) {
                        super.updateItem(item, empty);
                        if (empty || item == null) {
                            setText(null);
                        } else {
                            setText(item.getPrenom() + " " + item.getNom());
                        }
                    }
                });
                filterClientCombo.setButtonCell(new ListCell<Client>() {
                    @Override
                    protected void updateItem(Client item, boolean empty) {
                        super.updateItem(item, empty);
                        if (empty || item == null) {
                            setText("Client");
                        } else {
                            setText(item.getPrenom() + " " + item.getNom());
                        }
                    }
                });
            } catch (Exception e) {
                System.err.println("Error loading clients: " + e.getMessage());
                filterClientCombo.setItems(FXCollections.observableArrayList());
            }

            try {
                List<Vehicule> vehicules = vehiculeDAO.findAll();
                filterVehicleCombo.setItems(FXCollections.observableArrayList(vehicules));

                // Set up vehicle display
                filterVehicleCombo.setCellFactory(param -> new ListCell<Vehicule>() {
                    @Override
                    protected void updateItem(Vehicule item, boolean empty) {
                        super.updateItem(item, empty);
                        if (empty || item == null) {
                            setText(null);
                        } else {
                            setText(item.getMarque() + " " + item.getModele());
                        }
                    }
                });
                filterVehicleCombo.setButtonCell(new ListCell<Vehicule>() {
                    @Override
                    protected void updateItem(Vehicule item, boolean empty) {
                        super.updateItem(item, empty);
                        if (empty || item == null) {
                            setText("Véhicule");
                        } else {
                            setText(item.getMarque() + " " + item.getModele());
                        }
                    }
                });
            } catch (Exception e) {
                System.err.println("Error loading vehicles: " + e.getMessage());
                filterVehicleCombo.setItems(FXCollections.observableArrayList());
            }

            // Add listeners for real-time filtering
            filterStatusCombo.setOnAction(e -> applyFilters());
            filterMethodCombo.setOnAction(e -> applyFilters());
            filterClientCombo.setOnAction(e -> applyFilters());
            filterVehicleCombo.setOnAction(e -> applyFilters());
            filterDateFrom.setOnAction(e -> applyFilters());
            filterDateTo.setOnAction(e -> applyFilters());
            filterOverdueOnly.setOnAction(e -> applyFilters());
            filterPartialOnly.setOnAction(e -> applyFilters());

            // Text field listeners
            filterAmountMin.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
            filterAmountMax.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
            searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());

        } catch (Exception e) {
            System.err.println("Error setting up filter controls: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void loadData() {
        try {
            List<Paiement> payments = paiementDAO.findAll();

            allPayments = FXCollections.observableArrayList();
            for (Paiement payment : payments) {
                try {
                    PaymentRow row = new PaymentRow(payment);
                    allPayments.add(row);
                } catch (Exception e) {
                    // Only log critical errors, not every row creation issue
                    if (payment == null) {
                        System.err.println("Warning: Null payment found in database");
                    }
                }
            }

            filteredPayments = FXCollections.observableArrayList(allPayments);
            paymentsTable.setItems(filteredPayments);

        } catch (Exception e) {
            System.err.println("Error loading payment data: " + e.getMessage());

            // Initialize empty lists to prevent null pointer exceptions
            allPayments = FXCollections.observableArrayList();
            filteredPayments = FXCollections.observableArrayList();
            paymentsTable.setItems(filteredPayments);

            // Show error to user
            FXMLUtil.showError("Erreur lors du chargement des données: " + e.getMessage());
        }
    }

    private void applyFilters() {
        if (allPayments == null) return;

        List<PaymentRow> filtered = allPayments.stream()
            .filter(this::matchesFilters)
            .collect(Collectors.toList());

        filteredPayments.setAll(filtered);
        updateStatistics();
        updateSelectionCount();
    }

    private boolean matchesFilters(PaymentRow row) {
        try {
            Paiement payment = row.getPayment();
            if (payment == null) return false;

            // Status filter
            String selectedStatus = filterStatusCombo.getValue();
            if (selectedStatus != null && !"Tous".equals(selectedStatus)) {
                String statusName = row.getStatus();
                if (!selectedStatus.equals(statusName)) return false;
            }

            // Method filter
            String selectedMethod = filterMethodCombo.getValue();
            if (selectedMethod != null && !"Toutes".equals(selectedMethod)) {
                String methodName = row.getMethod();
                if (!selectedMethod.equals(methodName)) return false;
            }

            // Client filter
            Client selectedClient = filterClientCombo.getValue();
            if (selectedClient != null) {
                if (row.getClient() == null || !row.getClient().getId().equals(selectedClient.getId())) {
                    return false;
                }
            }

            // Vehicle filter
            Vehicule selectedVehicle = filterVehicleCombo.getValue();
            if (selectedVehicle != null) {
                if (row.getVehicule() == null || !row.getVehicule().getId().equals(selectedVehicle.getId())) {
                    return false;
                }
            }

            // Date range filter
            LocalDate fromDate = filterDateFrom.getValue();
            LocalDate toDate = filterDateTo.getValue();
            LocalDate paymentDate = payment.getDatePaiement();
            if (paymentDate != null) {
                if (fromDate != null && paymentDate.isBefore(fromDate)) return false;
                if (toDate != null && paymentDate.isAfter(toDate)) return false;
            }

            // Amount range filter
            try {
                String minText = filterAmountMin.getText();
                if (minText != null && !minText.trim().isEmpty()) {
                    double min = Double.parseDouble(minText.trim());
                    if (payment.getMontant() < min) return false;
                }

                String maxText = filterAmountMax.getText();
                if (maxText != null && !maxText.trim().isEmpty()) {
                    double max = Double.parseDouble(maxText.trim());
                    if (payment.getMontant() > max) return false;
                }
            } catch (NumberFormatException e) {
                // Invalid number format, ignore filter
            }

            // Overdue filter
            if (filterOverdueOnly.isSelected()) {
                LocalDate dueDate = payment.getDateEcheance();
                Paiement.Status status = payment.getStatus();
                if (status == Paiement.Status.PAYE || dueDate == null || !LocalDate.now().isAfter(dueDate)) {
                    return false;
                }
            }

            // Partial filter
            if (filterPartialOnly.isSelected()) {
                if (payment.getStatus() != Paiement.Status.PARTIEL) return false;
            }

            // Search filter
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase();
                return row.getClientName().toLowerCase().contains(searchLower) ||
                       row.getVehicleName().toLowerCase().contains(searchLower) ||
                       row.getReference().toLowerCase().contains(searchLower) ||
                       String.valueOf(payment.getMontant()).contains(searchLower);
            }

            return true;
        } catch (Exception e) {
            // Silently handle filter errors to avoid console spam
            return true; // Include the row if there's an error to avoid hiding data
        }
    }

    private void updateStatistics() {
        try {
            List<PaymentRow> payments = filteredPayments != null ? filteredPayments : new ArrayList<>();

            // Total revenue
            double totalRevenue = payments.stream()
                .filter(p -> p.getPayment() != null)
                .mapToDouble(p -> p.getPayment().getMontant())
                .sum();
            lblTotalRevenue.setText(String.format("%.2f DH", totalRevenue));

            // Paid payments
            long paidCount = payments.stream()
                .filter(p -> p.getPayment() != null && p.getPayment().getStatus() == Paiement.Status.PAYE)
                .count();
            lblPaidCount.setText(String.valueOf(paidCount));

            double paidPercentage = payments.isEmpty() ? 0 : (double) paidCount / payments.size() * 100;
            lblPaidPercentage.setText(String.format("%.1f%% du total", paidPercentage));

            // Pending payments
            long pendingCount = payments.stream()
                .filter(p -> p.getPayment() != null && p.getPayment().getStatus() == Paiement.Status.EN_ATTENTE)
                .count();
            lblPendingCount.setText(String.valueOf(pendingCount));

            double pendingAmount = payments.stream()
                .filter(p -> p.getPayment() != null && p.getPayment().getStatus() == Paiement.Status.EN_ATTENTE)
                .mapToDouble(p -> p.getPayment().getMontant())
                .sum();
            lblPendingAmount.setText(String.format("%.2f DH à recevoir", pendingAmount));

            // Overdue payments
            long overdueCount = payments.stream()
                .filter(p -> {
                    if (p.getPayment() == null) return false;
                    LocalDate dueDate = p.getPayment().getDateEcheance();
                    return p.getPayment().getStatus() != Paiement.Status.PAYE &&
                           dueDate != null && LocalDate.now().isAfter(dueDate);
                })
                .count();
            lblOverdueCount.setText(String.valueOf(overdueCount));

            double overdueAmount = payments.stream()
                .filter(p -> {
                    if (p.getPayment() == null) return false;
                    LocalDate dueDate = p.getPayment().getDateEcheance();
                    return p.getPayment().getStatus() != Paiement.Status.PAYE &&
                           dueDate != null && LocalDate.now().isAfter(dueDate);
                })
                .mapToDouble(p -> p.getPayment().getMontant())
                .sum();
            lblOverdueAmount.setText(String.format("%.2f DH en retard", overdueAmount));

            // Update charts
            updatePaymentMethodsChart(payments);
            updateMonthlyTrendsChart(payments);

            // Bottom statistics
            lblTotalCount.setText(String.format("Total: %d paiements", payments.size()));
            lblFilteredRevenue.setText(String.format("Revenus filtrés: %.2f DH", totalRevenue));

        } catch (Exception e) {
            System.err.println("Error updating statistics: " + e.getMessage());

            // Set default values on error
            lblTotalRevenue.setText("0.00 DH");
            lblPaidCount.setText("0");
            lblPaidPercentage.setText("0% du total");
            lblPendingCount.setText("0");
            lblPendingAmount.setText("0.00 DH à recevoir");
            lblOverdueCount.setText("0");
            lblOverdueAmount.setText("0.00 DH en retard");
            lblTotalCount.setText("Total: 0 paiements");
            lblFilteredRevenue.setText("Revenus filtrés: 0.00 DH");
        }
    }

    private void updatePaymentMethodsChart(List<PaymentRow> payments) {
        try {
            paymentMethodsChart.getChildren().clear();

            Map<String, Long> methodCounts = payments.stream()
                .filter(p -> p.getMethod() != null)
                .collect(Collectors.groupingBy(PaymentRow::getMethod, Collectors.counting()));

            if (methodCounts.isEmpty()) {
                Label noDataLabel = new Label("Aucune donnée disponible");
                noDataLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6b7280; -fx-font-style: italic;");
                paymentMethodsChart.getChildren().add(noDataLabel);
                return;
            }

            methodCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .forEach(entry -> {
                    HBox methodRow = new HBox(10);
                    methodRow.setAlignment(Pos.CENTER_LEFT);

                    Label methodLabel = new Label(entry.getKey());
                    methodLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

                    Label countLabel = new Label(entry.getValue().toString());
                    countLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #1f2937;");

                    methodRow.getChildren().addAll(methodLabel, countLabel);
                    paymentMethodsChart.getChildren().add(methodRow);
                });
        } catch (Exception e) {
            // Silently handle chart errors to avoid console spam
            paymentMethodsChart.getChildren().clear();
            Label errorLabel = new Label("Erreur de chargement");
            errorLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #ef4444;");
            paymentMethodsChart.getChildren().add(errorLabel);
        }
    }

    private void updateMonthlyTrendsChart(List<PaymentRow> payments) {
        try {
            monthlyTrendsChart.getChildren().clear();

            // Simple monthly trend display
            Map<String, Double> monthlyRevenue = payments.stream()
                .filter(p -> p.getPayment() != null && p.getPayment().getDatePaiement() != null)
                .collect(Collectors.groupingBy(
                    p -> p.getPayment().getDatePaiement().format(DateTimeFormatter.ofPattern("MM/yyyy")),
                    Collectors.summingDouble(p -> p.getPayment().getMontant())
                ));

            if (monthlyRevenue.isEmpty()) {
                Label noDataLabel = new Label("Aucune donnée disponible");
                noDataLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6b7280; -fx-font-style: italic;");
                monthlyTrendsChart.getChildren().add(noDataLabel);
                return;
            }

            monthlyRevenue.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByKey().reversed())
                .limit(6)
                .forEach(entry -> {
                    HBox monthRow = new HBox(10);
                    monthRow.setAlignment(Pos.CENTER_LEFT);

                    Label monthLabel = new Label(entry.getKey());
                    monthLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #374151;");

                    Label revenueLabel = new Label(String.format("%.2f DH", entry.getValue()));
                    revenueLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #1f2937;");

                    monthRow.getChildren().addAll(monthLabel, revenueLabel);
                    monthlyTrendsChart.getChildren().add(monthRow);
                });
        } catch (Exception e) {
            // Silently handle chart errors to avoid console spam
            monthlyTrendsChart.getChildren().clear();
            Label errorLabel = new Label("Erreur de chargement");
            errorLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #ef4444;");
            monthlyTrendsChart.getChildren().add(errorLabel);
        }
    }

    private void showPaymentDetails(PaymentRow row) {
        if (row == null) return;

        selectedPayment = row;
        Paiement payment = row.getPayment();

        lblSelectedPayment.setText("Paiement #" + payment.getId());

        paymentDetails.getChildren().clear();

        // Add payment details
        addDetailRow("Client:", row.getClientName());
        addDetailRow("Véhicule:", row.getVehicleName());
        addDetailRow("Montant:", String.format("%.2f DH", payment.getMontant()));
        addDetailRow("Méthode:", row.getMethod());
        addDetailRow("Statut:", row.getStatus());
        addDetailRow("Date:", row.getDate());
        addDetailRow("Échéance:", row.getDueDate());
        addDetailRow("Référence:", row.getReference());

        // Update timeline
        updatePaymentTimeline(payment);
    }

    private void addDetailRow(String label, String value) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);

        Label labelNode = new Label(label);
        labelNode.setStyle("-fx-font-weight: bold; -fx-text-fill: #374151; -fx-min-width: 80px;");

        Label valueNode = new Label(value);
        valueNode.setStyle("-fx-text-fill: #1f2937;");

        row.getChildren().addAll(labelNode, valueNode);
        paymentDetails.getChildren().add(row);
    }

    private void updatePaymentTimeline(Paiement payment) {
        paymentTimeline.getChildren().clear();

        // Add timeline events
        addTimelineEvent("Création", payment.getDatePaiement(), "Paiement créé");

        if (payment.getStatus() == Paiement.Status.PAYE) {
            addTimelineEvent("Paiement", payment.getDatePaiement(), "Paiement effectué");
        }
    }

    private void addTimelineEvent(String title, LocalDate date, String description) {
        VBox event = new VBox(5);
        event.setStyle("-fx-padding: 8px; -fx-border-color: #e5e7eb; -fx-border-width: 0 0 1px 0;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #1f2937;");

        Label dateLabel = new Label(date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A");
        dateLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #6b7280;");

        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #374151;");

        event.getChildren().addAll(titleLabel, dateLabel, descLabel);
        paymentTimeline.getChildren().add(event);
    }

    private void updateSelectionCount() {
        long selectedCount = filteredPayments.stream()
            .filter(PaymentRow::isSelected)
            .count();
        lblSelectedCount.setText(String.format("Sélectionnés: %d", selectedCount));
    }

    private void updateLastUpdateTime() {
        lblLastUpdate.setText("Dernière mise à jour: " +
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    private void editPayment(PaymentRow row) {
      Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Modifier Paiement");
        alert.setHeaderText("Fonctionnalité à venir");
        alert.setContentText("Fonctionnalité de modification de paiement à implémenter");
        alert.showAndWait();
    }

    // Event Handlers
    @FXML private void handleRefresh() {
        loadData();
        updateStatistics();
        updateLastUpdateTime();
    }

    @FXML private void handleClearFilters() {
        filterStatusCombo.setValue("Tous");
        filterMethodCombo.setValue("Toutes");
        filterClientCombo.setValue(null);
        filterVehicleCombo.setValue(null);
        filterDateFrom.setValue(null);
        filterDateTo.setValue(null);
        filterAmountMin.clear();
        filterAmountMax.clear();
        filterOverdueOnly.setSelected(false);
        filterPartialOnly.setSelected(false);
        searchField.clear();
        applyFilters();
    }

    @FXML private void handleExportCSV() {
        try {
            FXMLUtil.showSuccess("Export CSV en cours de développement.");
        } catch (Exception e) {
            System.err.println("Error in handleExportCSV: " + e.getMessage());
        }
    }

    @FXML private void handleExportExcel() {
        try {
            FXMLUtil.showInfo("Export Excel en cours de développement.");
        } catch (Exception e) {
            System.err.println("Error in handleExportExcel: " + e.getMessage());
        }
    }

    @FXML private void handleExportPDF() {
        try {
            FXMLUtil.showInfo("Export PDF en cours de développement.");
        } catch (Exception e) {
            System.err.println("Error in handleExportPDF: " + e.getMessage());
        }
    }

    @FXML private void handleExportUnpaid() {
        try {
            FXMLUtil.showInfo("Export des impayés en cours de développement.");
        } catch (Exception e) {
            System.err.println("Error in handleExportUnpaid: " + e.getMessage());
        }
    }

    @FXML private void handleExportOverdue() {
        try {
            FXMLUtil.showInfo("Export des retards en cours de développement.");
        } catch (Exception e) {
            System.err.println("Error in handleExportOverdue: " + e.getMessage());
        }
    }

    @FXML private void handleBulkActions() {
        List<PaymentRow> selected = filteredPayments.stream()
            .filter(PaymentRow::isSelected)
            .collect(Collectors.toList());

        if (selected.isEmpty()) {
            FXMLUtil.showWarning("Veuillez sélectionner au moins un paiement.");
            return;
        }

        // Create bulk actions dialog
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("Actions Groupées");
        dialog.setHeaderText(String.format("Actions pour %d paiements sélectionnés", selected.size()));

        // Set the button types
        ButtonType markPaidButtonType = new ButtonType("Marquer Payé", ButtonBar.ButtonData.OK_DONE);
        ButtonType exportButtonType = new ButtonType("Exporter", ButtonBar.ButtonData.OTHER);
        dialog.getDialogPane().getButtonTypes().addAll(markPaidButtonType, exportButtonType, ButtonType.CANCEL);

        // Create the content
        VBox content = new VBox(10);
        content.getChildren().add(new Label("Choisissez une action à appliquer aux paiements sélectionnés:"));

        // Add summary of selected payments
        double totalAmount = selected.stream()
            .mapToDouble(p -> p.getPayment().getMontant())
            .sum();
        content.getChildren().add(new Label(String.format("Montant total: %.2f DH", totalAmount)));

        dialog.getDialogPane().setContent(content);

        // Convert the result to a string when the button is clicked
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == markPaidButtonType) {
                return "MARK_PAID";
            } else if (dialogButton == exportButtonType) {
                return "EXPORT";
            }
            return null;
        });

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(action -> {
            switch (action) {
                case "MARK_PAID":
                    handleBulkMarkPaid(selected);
                    break;
                case "EXPORT":
                    handleBulkExport(selected);
                    break;
            }
        });
    }

    private void handleBulkMarkPaid(List<PaymentRow> selected) {
        try {
            int updatedCount = 0;
            for (PaymentRow row : selected) {
                Paiement payment = row.getPayment();
                if (payment.getStatus() != Paiement.Status.PAYE) {
                    payment.setStatus(Paiement.Status.PAYE);
                    payment.setDatePaiement(LocalDate.now());
                    paiementDAO.save(payment);
                    updatedCount++;
                }
            }

            // Refresh data
            loadData();
            updateStatistics();

            FXMLUtil.showSuccess(String.format("%d paiements marqués comme payés.", updatedCount));
        } catch (Exception e) {
            System.err.println("Error in bulk mark paid: " + e.getMessage());
            e.printStackTrace();
            FXMLUtil.showError("Erreur lors de la mise à jour des paiements: " + e.getMessage());
        }
    }

    private void handleBulkExport(List<PaymentRow> selected) {
        try {
            // Simple CSV export for now
            StringBuilder csv = new StringBuilder();
            csv.append("ID,Client,Véhicule,Montant,Méthode,Date,Statut\n");

            for (PaymentRow row : selected) {
                Paiement p = row.getPayment();
                csv.append(String.format("%d,%s,%s,%.2f,%s,%s,%s\n",
                    p.getId(),
                    row.getClientName().replace(",", ";"),
                    row.getVehicleName().replace(",", ";"),
                    p.getMontant(),
                    row.getMethod(),
                    row.getDate(),
                    row.getStatus()
                ));
            }

            // For now, just show the CSV content
            TextArea textArea = new TextArea(csv.toString());
            textArea.setEditable(false);
            textArea.setPrefRowCount(10);
            textArea.setPrefColumnCount(50);

            Dialog<Void> exportDialog = new Dialog<>();
            exportDialog.setTitle("Export CSV");
            exportDialog.setHeaderText("Données exportées (copiez le contenu)");
            exportDialog.getDialogPane().setContent(textArea);
            exportDialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);
            exportDialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Error in bulk export: " + e.getMessage());
            e.printStackTrace();
            FXMLUtil.showError("Erreur lors de l'export: " + e.getMessage());
        }
    }

    @FXML private void handleMarkPaid() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }

        try {
            Paiement payment = selectedPayment.getPayment();
            if (payment.getStatus() == Paiement.Status.PAYE) {
                FXMLUtil.showInfo("Ce paiement est déjà marqué comme payé.");
                return;
            }

            // Confirm action
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("Confirmer le paiement");
            confirmAlert.setHeaderText("Marquer comme payé");
            confirmAlert.setContentText("Êtes-vous sûr de vouloir marquer ce paiement comme payé ?");

            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                // Update payment status
                payment.setStatus(Paiement.Status.PAYE);
                payment.setDatePaiement(LocalDate.now());

                // Save to database
                paiementDAO.save(payment);

                // Refresh data and UI
                loadData();
                updateStatistics();
                showPaymentDetails(selectedPayment);

                FXMLUtil.showSuccess("Paiement marqué comme payé avec succès.");
            }
        } catch (Exception e) {
            System.err.println("Error marking payment as paid: " + e.getMessage());
            e.printStackTrace();
            FXMLUtil.showError("Erreur lors de la mise à jour du paiement: " + e.getMessage());
        }
    }

    @FXML private void handleSendReminder() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }
        FXMLUtil.showInfo("Envoi de rappel en cours de développement.");
    }

    @FXML private void handleViewContract() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }
        FXMLUtil.showInfo("Visualisation du contrat en cours de développement.");
    }

    @FXML private void handleGenerateInvoice() {
        if (selectedPayment == null) {
            FXMLUtil.showWarning("Veuillez sélectionner un paiement.");
            return;
        }
        FXMLUtil.showInfo("Génération de facture en cours de développement.");
    }

    @FXML private void handleReconcile() {
        FXMLUtil.showInfo("Rapprochement bancaire en cours de développement.");
    }

    @FXML private void handleAnalytics() {
        FXMLUtil.showInfo("Analyses avancées en cours de développement.");
    }

    @FXML private void handleSearch() {
        applyFilters();
    }

    @FXML private void closeWindow() {
        paymentsTable.getScene().getWindow().hide();
    }
}

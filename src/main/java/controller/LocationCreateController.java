package controller;

import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;
import model.Client;
import model.Location;
import model.Vehicule;
import dao.ClientDAO;
import dao.LocationDAO;
import dao.VehiculeDAO;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import javafx.stage.FileChooser;
import java.io.File;
import java.io.IOException;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType1Font;

public class LocationCreateController {
    @FXML private ComboBox<Client> clientComboBox;
    @FXML private ComboBox<Vehicule> vehiculeComboBox;
    @FXML private TextField vehiculeSearchField;
    @FXML private TextField clientSearchField;
    @FXML private ImageView vehiculeImage;
    @FXML private Label vehiculeDetailsLabel;
    @FXML private TextField contractNumberField;
    @FXML private TextField agentField;
    @FXML private DatePicker dateDebutPicker;
    @FXML private DatePicker dateFinPicker;
    @FXML private TextField pickupLocationField;
    @FXML private TextField deliveryLocationField;
    @FXML private ComboBox<String> insuranceTypeComboBox;
    @FXML private ComboBox<String> fuelPolicyComboBox;
    @FXML private ComboBox<String> paiementComboBox;
    @FXML private CheckBox optionAssurance;
    @FXML private CheckBox optionGPS;
    @FXML private CheckBox optionSiegeBebe;
    @FXML private CheckBox optionAdditionalDriver;
    @FXML private TextArea notesField;
    @FXML private Label totalPrixLabel;
    @FXML private VBox newClientForm;
    @FXML private TextField newNomField;
    @FXML private TextField newPrenomField;
    @FXML private TextField newEmailField;
    @FXML private TextField newTelField;

    private final ClientDAO clientDAO = new ClientDAO();
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private final LocationDAO locationDAO = new LocationDAO();
    private ObservableList<Client> allClients;
    private ObservableList<Vehicule> allVehicules;
    private double prixParJour = 0;

    private static model.Location locationToEdit = null;
    public static void setLocationToEdit(model.Location loc) { locationToEdit = loc; }

    public void prefillForm(model.Location location) {
        if (location == null) return;
        clientComboBox.setValue(location.getClient());
        vehiculeComboBox.setValue(location.getVehicule());
        dateDebutPicker.setValue(location.getDateDebut());
        dateFinPicker.setValue(location.getDateFinPrevue());
        // Optionally fill other fields if present
        // ...
    }

    @FXML
    public void initialize() {
        // Load all clients and vehicles
        allClients = FXCollections.observableArrayList(clientDAO.findAll());
        allVehicules = FXCollections.observableArrayList(vehiculeDAO.findAll());
        clientComboBox.setItems(allClients);
        vehiculeComboBox.setItems(allVehicules);

        // Set custom cell factory and string converter for vehiculeComboBox
        vehiculeComboBox.setCellFactory(cb -> new javafx.scene.control.ListCell<model.Vehicule>() {
            @Override
            protected void updateItem(model.Vehicule v, boolean empty) {
                super.updateItem(v, empty);
                if (empty || v == null) {
                    setText(null);
                } else {
                    setText(v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : ""));
                }
            }
        });
        vehiculeComboBox.setButtonCell(new javafx.scene.control.ListCell<model.Vehicule>() {
            @Override
            protected void updateItem(model.Vehicule v, boolean empty) {
                super.updateItem(v, empty);
                if (empty || v == null) {
                    setText(null);
                } else {
                    setText(v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : ""));
                }
            }
        });
        vehiculeComboBox.setConverter(new javafx.util.StringConverter<model.Vehicule>() {
            @Override
            public String toString(model.Vehicule v) {
                if (v == null) return "";
                return v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : "");
            }
            @Override
            public model.Vehicule fromString(String s) {
                return null; // Not needed
            }
        });

        // Set custom cell factory and string converter for clientComboBox
        clientComboBox.setCellFactory(cb -> new javafx.scene.control.ListCell<model.Client>() {
            @Override
            protected void updateItem(model.Client c, boolean empty) {
                super.updateItem(c, empty);
                if (empty || c == null) {
                    setText(null);
                } else {
                    setText(c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : ""));
                }
            }
        });
        clientComboBox.setButtonCell(new javafx.scene.control.ListCell<model.Client>() {
            @Override
            protected void updateItem(model.Client c, boolean empty) {
                super.updateItem(c, empty);
                if (empty || c == null) {
                    setText(null);
                } else {
                    setText(c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : ""));
                }
            }
        });
        clientComboBox.setConverter(new javafx.util.StringConverter<model.Client>() {
            @Override
            public String toString(model.Client c) {
                if (c == null) return "";
                return c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : "");
            }
            @Override
            public model.Client fromString(String s) {
                return null; // Not needed
            }
        });

        // Auto-fill agent and contract number
        agentField.setText(getCurrentAgentName());
        contractNumberField.setText(generateContractNumber());
        // Disable editing
        agentField.setEditable(false);
        contractNumberField.setEditable(false);
        agentField.setStyle("-fx-opacity: 0.7;");
        contractNumberField.setStyle("-fx-opacity: 0.7;");
        // Hide new client form by default
        newClientForm.setVisible(false);
        // Listeners for search fields
        vehiculeSearchField.textProperty().addListener((obs, oldVal, newVal) -> filterVehicules(newVal));
        clientSearchField.textProperty().addListener((obs, oldVal, newVal) -> filterClients(newVal));
        // Listeners for combo selection
        vehiculeComboBox.valueProperty().addListener((obs, oldV, newV) -> updateVehiculeDetails(newV));
        // Default selection with error handling
        try {
            if (!allVehicules.isEmpty()) vehiculeComboBox.getSelectionModel().selectFirst();
            if (!allClients.isEmpty()) clientComboBox.getSelectionModel().selectFirst();
        } catch (Exception e) {
            System.err.println("Error in default selection: " + e.getMessage());
        }

        // Update price on date change
        dateDebutPicker.valueProperty().addListener((obs, oldV, newV) -> updateTotalPrix());
        dateFinPicker.valueProperty().addListener((obs, oldV, newV) -> updateTotalPrix());

        // Populate insurance, fuel, payment
        insuranceTypeComboBox.setItems(FXCollections.observableArrayList("Standard", "Tous risques", "Premium"));
        fuelPolicyComboBox.setItems(FXCollections.observableArrayList("Plein à plein", "Plein à vide", "Retour identique"));
        paiementComboBox.setItems(FXCollections.observableArrayList("Espèces", "Carte bancaire", "Virement"));

        try {
            if (!insuranceTypeComboBox.getItems().isEmpty()) insuranceTypeComboBox.getSelectionModel().selectFirst();
            if (!fuelPolicyComboBox.getItems().isEmpty()) fuelPolicyComboBox.getSelectionModel().selectFirst();
            if (!paiementComboBox.getItems().isEmpty()) paiementComboBox.getSelectionModel().selectFirst();
        } catch (Exception e) {
            System.err.println("Error in combo box selection: " + e.getMessage());
        }
        // Initial price
        updateVehiculeDetails(vehiculeComboBox.getValue());
        // If editing, prefill form
        if (locationToEdit != null) {
            prefillForm(locationToEdit);
            locationToEdit = null;
        }
    }

    private void filterVehicules(String keyword) {
        if (keyword == null || keyword.isBlank()) {
            vehiculeComboBox.setItems(allVehicules);
            return;
        }
        String lower = keyword.toLowerCase();
        vehiculeComboBox.setItems(allVehicules.filtered(v ->
            v.getMarque().toLowerCase().contains(lower) ||
            v.getModele().toLowerCase().contains(lower) ||
            (v.getImmatriculation() != null && v.getImmatriculation().toLowerCase().contains(lower))
        ));
    }

    private void filterClients(String keyword) {
        if (keyword == null || keyword.isBlank()) {
            clientComboBox.setItems(allClients);
            return;
        }
        String lower = keyword.toLowerCase();
        clientComboBox.setItems(allClients.filtered(c ->
            c.getNom().toLowerCase().contains(lower) ||
            c.getPrenom().toLowerCase().contains(lower) ||
            (c.getEmail() != null && c.getEmail().toLowerCase().contains(lower)) ||
            (c.getTelephone() != null && c.getTelephone().toLowerCase().contains(lower))
        ));
    }

    private void updateVehiculeDetails(Vehicule v) {
        if (v == null) {
            vehiculeDetailsLabel.setText("");
            vehiculeImage.setImage(null);
            prixParJour = 0;
            updateTotalPrix();
            return;
        }
        String details = v.getMarque() + " " + v.getModele() + "\n" +
                "Immatriculation: " + v.getImmatriculation() + "\n" +
                "Prix: " + (v.getPrixParJour() != null ? v.getPrixParJour() : 0.0) + " DH/jour\n" +
                "État: " + v.getEtat() + "\n" +
                "Carburant: " + v.getCarburant() + "\n" +
                "Métrage: " + v.getMetrage() + " km";
        vehiculeDetailsLabel.setText(details);
        if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
            try {
                vehiculeImage.setImage(new Image(v.getPhotoUrl(), 120, 80, true, true));
            } catch (Exception e) { vehiculeImage.setImage(null); }
        } else {
            vehiculeImage.setImage(null);
        }
        prixParJour = v.getPrixParJour() != null ? v.getPrixParJour() : 0.0;
        updateTotalPrix();
    }

    private void updateTotalPrix() {
        if (dateDebutPicker == null || dateFinPicker == null || dateDebutPicker.getValue() == null || dateFinPicker.getValue() == null) {
            totalPrixLabel.setText(String.format("%.2f DH", prixParJour));
            return;
        }
        long days = java.time.temporal.ChronoUnit.DAYS.between(dateDebutPicker.getValue(), dateFinPicker.getValue());
        if (days < 1) days = 1;
        double total = days * prixParJour;
        if (optionAssurance != null && optionAssurance.isSelected()) total += 50;
        if (optionGPS != null && optionGPS.isSelected()) total += 20;
        if (optionSiegeBebe != null && optionSiegeBebe.isSelected()) total += 15;
        if (optionAdditionalDriver != null && optionAdditionalDriver.isSelected()) total += 30;
        if (insuranceTypeComboBox != null && insuranceTypeComboBox.getValue() != null) {
            switch (insuranceTypeComboBox.getValue()) {
                case "Tous risques": total += 100; break;
                case "Premium": total += 200; break;
            }
        }
        totalPrixLabel.setText(String.format("%.2f DH", total));
    }

    private String getCurrentAgentName() {
        // TODO: Replace with actual session/user logic
        return "Agent Admin";
    }

    private String generateContractNumber() {
        // Example: generate a random contract number
        return "CTR-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    @FXML
    private void handleShowNewClientForm() {
        newClientForm.setVisible(true);
    }

    @FXML
    private void handleCancelNewClient() {
        newClientForm.setVisible(false);
        newNomField.clear();
        newPrenomField.clear();
        newEmailField.clear();
        newTelField.clear();
    }

    @FXML
    private void handleSaveNewClient() {
        String nom = newNomField.getText();
        String prenom = newPrenomField.getText();
        String email = newEmailField.getText();
        String tel = newTelField.getText();
        if (nom.isBlank() || prenom.isBlank()) return;
        Client c = new Client();
        c.setNom(nom);
        c.setPrenom(prenom);
        c.setEmail(email);
        c.setTelephone(tel);
        clientDAO.save(c);
        allClients.add(c);
        clientComboBox.getItems().add(c);
        clientComboBox.getSelectionModel().select(c);
        handleCancelNewClient();
    }

    @FXML
    private void handleExportContrat() {
        // Validate that we have the required data
        if (vehiculeComboBox.getValue() == null || clientComboBox.getValue() == null) {
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle("Données manquantes");
            alert.setHeaderText("Impossible d'exporter le contrat");
            alert.setContentText("Veuillez sélectionner un véhicule et un client avant d'exporter le contrat.");
            alert.showAndWait();
            return;
        }

        try {
            // File chooser for PDF
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Sauvegarder le contrat de location");
            fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("PDF Files", "*.pdf"));
            
            File file = fileChooser.showSaveDialog(vehiculeComboBox.getScene().getWindow());
            
            if (file != null) {
                generateContractPDF(file);
                
                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("Contrat exporté");
                alert.setHeaderText("Succès");
                alert.setContentText("Le contrat a été exporté avec succès vers:\n" + file.getAbsolutePath());
                alert.showAndWait();
            }
        } catch (Exception e) {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur d'export");
            alert.setHeaderText("Erreur lors de l'export du contrat");
            alert.setContentText("Erreur: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private void generateContractPDF(File file) throws IOException {
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            doc.addPage(page);
            PDPageContentStream content = new PDPageContentStream(doc, page);
            
            float y = page.getMediaBox().getHeight() - 40;
            float margin = 40;
            float leading = 18;
            
            // Title
            content.setFont(PDType1Font.HELVETICA_BOLD, 20);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("CONTRAT DE LOCATION DE VÉHICULE");
            content.endText();
            y -= 40;
            
            // Contract number and date
            content.setFont(PDType1Font.HELVETICA_BOLD, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Numéro de contrat: " + contractNumberField.getText());
            content.endText();
            y -= 25;
            
            content.setFont(PDType1Font.HELVETICA, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Date: " + LocalDate.now().toString());
            content.endText();
            y -= 40;
            
            // Client information
            content.setFont(PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("INFORMATIONS DU CLIENT");
            content.endText();
            y -= 25;
            
            Client client = clientComboBox.getValue();
            content.setFont(PDType1Font.HELVETICA, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Nom: " + client.getNom() + " " + client.getPrenom());
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("CIN: " + client.getCin());
            content.endText();
            y -= 20;
            
            if (client.getTelephone() != null && !client.getTelephone().isEmpty()) {
                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Téléphone: " + client.getTelephone());
                content.endText();
                y -= 20;
            }
            
            if (client.getEmail() != null && !client.getEmail().isEmpty()) {
                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText("Email: " + client.getEmail());
                content.endText();
                y -= 20;
            }
            
            y -= 20;
            
            // Vehicle information
            content.setFont(PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("INFORMATIONS DU VÉHICULE");
            content.endText();
            y -= 25;
            
            Vehicule vehicule = vehiculeComboBox.getValue();
            content.setFont(PDType1Font.HELVETICA, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Marque: " + vehicule.getMarque());
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Modèle: " + vehicule.getModele());
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Immatriculation: " + vehicule.getImmatriculation());
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Prix par jour: " + (vehicule.getPrixParJour() != null ? vehicule.getPrixParJour() : 0.0) + " DH");
            content.endText();
            y -= 40;
            
            // Rental details
            content.setFont(PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("DÉTAILS DE LA LOCATION");
            content.endText();
            y -= 25;
            
            content.setFont(PDType1Font.HELVETICA, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Date de début: " + (dateDebutPicker.getValue() != null ? dateDebutPicker.getValue().toString() : "À définir"));
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Date de fin: " + (dateFinPicker.getValue() != null ? dateFinPicker.getValue().toString() : "À définir"));
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Lieu de prise en charge: " + (pickupLocationField.getText() != null ? pickupLocationField.getText() : "À définir"));
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Lieu de retour: " + (deliveryLocationField.getText() != null ? deliveryLocationField.getText() : "À définir"));
            content.endText();
            y -= 20;
            
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Méthode de paiement: " + (paiementComboBox.getValue() != null ? paiementComboBox.getValue() : "À définir"));
            content.endText();
            y -= 40;
            
            // Total price
            content.setFont(PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("MONTANT TOTAL: " + totalPrixLabel.getText());
            content.endText();
            y -= 40;
            
            // Terms and conditions
            content.setFont(PDType1Font.HELVETICA_BOLD, 14);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("CONDITIONS GÉNÉRALES");
            content.endText();
            y -= 25;
            
            content.setFont(PDType1Font.HELVETICA, 10);
            String[] terms = {
                "1. Le client s'engage à utiliser le véhicule conformément au code de la route.",
                "2. Le client est responsable de tout dommage causé au véhicule pendant la location.",
                "3. Le véhicule doit être retourné dans l'état où il a été reçu.",
                "4. En cas de retard, des frais supplémentaires seront appliqués.",
                "5. Le carburant doit être retourné selon la politique choisie.",
                "6. L'assurance couvre les dommages selon le type choisi."
            };
            
            for (String term : terms) {
                if (y < 60) {
                    content.close();
                    page = new PDPage(PDRectangle.A4);
                    doc.addPage(page);
                    content = new PDPageContentStream(doc, page);
                    y = page.getMediaBox().getHeight() - 40;
                }
                
                content.beginText();
                content.newLineAtOffset(margin, y);
                content.showText(term);
                content.endText();
                y -= 15;
            }
            
            content.close();
            doc.save(file.getAbsolutePath());
        }
    }

    @FXML
    private void handleConfirm() {
        Client client = clientComboBox.getValue();
        Vehicule vehicule = vehiculeComboBox.getValue();
        if (client == null || vehicule == null || dateDebutPicker.getValue() == null || dateFinPicker.getValue() == null) return;
        java.time.LocalDate dateDebut = dateDebutPicker.getValue();
        java.time.LocalDate dateFin = dateFinPicker.getValue();
        if (dateFin.isBefore(dateDebut)) {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
            alert.setTitle("Erreur");
            alert.setHeaderText("Dates invalides");
            alert.setContentText("La date de fin doit être postérieure à la date de début.");
            alert.showAndWait();
            return;
        }
        boolean available = locationDAO.isVehiculeAvailable(
            vehicule.getId(), dateDebut, dateFin, null
        );
        if (!available) {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
            alert.setTitle("Conflit de réservation");
            alert.setHeaderText("Véhicule déjà réservé");
            alert.setContentText("Ce véhicule est déjà réservé pour cette période.");
            alert.showAndWait();
            return;
        }
        Location location = new Location();
        location.setClient(client);
        location.setVehicule(vehicule);
        location.setDateDebut(dateDebut);
        location.setDateFinPrevue(dateFin);
        location.setPrixTotal(Double.parseDouble(totalPrixLabel.getText().replace(" DH", "")));
        location.updateStatus();
        // TODO: Set other fields (pickup, delivery, insurance, etc.) if model supports
        locationDAO.save(location);

        // Send notification for location confirmation
        if (controller.LoginController.loggedInUser instanceof model.User) {
            model.User currentUser = (model.User) controller.LoginController.loggedInUser;
            service.NotificationService notificationService = service.NotificationService.getInstance();
            notificationService.createLocationConfirmedNotification(location, currentUser);
        }

        // Show confirmation dialog
        javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
        alert.setTitle("Succès");
        alert.setHeaderText("Location créée avec succès");
        alert.setContentText("La nouvelle location a été enregistrée. Redirection vers la liste des locations...");

        alert.showAndWait().ifPresent(response -> {
            // Redirect to location.fxml
            try {
                util.FXMLUtil.loadView("/view/location.fxml", "Gestion des Locations");
                // Close current window
                ((javafx.stage.Stage) clientComboBox.getScene().getWindow()).close();
            } catch (Exception e) {
                System.err.println("Erreur lors de la redirection: " + e.getMessage());
                e.printStackTrace();
            }
        });
        // Redirect to location page in dashboard
        javafx.scene.Scene scene = clientComboBox.getScene();
        javafx.scene.Node node = scene.lookup("#contentPane");
        if (node instanceof javafx.scene.layout.StackPane contentPane) {
            try {
                javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/location.fxml"));
                javafx.scene.Parent locationRoot = loader.load();
                contentPane.getChildren().setAll(locationRoot);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @FXML
    private void handleCancel() {
        // TODO: Implement navigation back to previous page or dashboard
    }

    public void setVehicule(Vehicule vehicule) {
        try {
            if (vehicule != null) {
                vehiculeComboBox.getSelectionModel().select(vehicule);
                updateVehiculeDetails(vehicule);
            } else {
                vehiculeComboBox.getSelectionModel().clearSelection();
                vehiculeDetailsLabel.setText("");
                if (vehiculeImage != null) vehiculeImage.setImage(null);
                prixParJour = 0;
                updateTotalPrix();
            }
        } catch (Exception e) {
            System.err.println("Error setting vehicle: " + e.getMessage());
            vehiculeDetailsLabel.setText("Erreur lors du chargement du véhicule");
            prixParJour = 0;
            updateTotalPrix();
        }
    }

    // Extension logic stub (to be implemented in UI)
    // public void handleProlongerLocation() { ... }
}

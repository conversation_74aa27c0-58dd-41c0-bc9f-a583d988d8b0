package controller;

import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;
import model.Location;
import model.Client;
import model.User;
import model.Vehicule;
import javafx.stage.Stage;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import javafx.stage.FileChooser;
import javafx.scene.control.Alert;
import util.FXMLUtil;

import java.io.File;
import java.io.IOException;

public class LocationDetailController {
    @FXML private Label lblClient;
    @FXML private Label lblVehicule;
    @FXML private Label lblLocationDates;
    @FXML private Label lblStatus;
    @FXML private Label lblPrixTotal;
    @FXML private Label lblPenalite;
    @FXML private Label lblCreatedBy;
    @FXML private ImageView imgVehicule;
    @FXML private Label lblVehiculeMarque;
    @FXML private Label lblVehiculeModele;
    @FXML private Label lblVehiculeImmat;
    @FXML private Label lblVehiculeEtat;
    @FXML private Label lblVehiculeCarburant;
    @FXML private Label lblVehiculePrixJour;
    @FXML private Label lblVehiculeMetrage;
    @FXML private Label lblClientNom;
    @FXML private Label lblClientPrenom;
    @FXML private Label lblClientCIN;
    @FXML private Label lblClientTel;
    @FXML private Label lblClientEmail;
    @FXML private Label lblLocationDebut;
    @FXML private Label lblLocationFinPrevue;
    @FXML private Label lblLocationFinReelle;
    @FXML private VBox root;

    @FXML
    private void handleModify() {
        // Only allow for admin (pseudo-check, replace with real role check)
        if (!isAdmin()) {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.WARNING);
            alert.setTitle("Accès refusé");
            alert.setHeaderText(null);
            alert.setContentText("Seul un administrateur peut modifier cette location.");
            alert.showAndWait();
            return;
        }
        // Use the main location controller for editing
        try {
            FXMLUtil.createMaximizedWindow("/view/location.fxml", "Modifier la Location", getClass());
            closeWindow();
        } catch (Exception e) {
            showAlert("Erreur lors de l'ouverture du formulaire: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private boolean isAdmin() {
        // Check if logged in user is admin
        if (LoginController.loggedInUser != null) {
            try {
                User user = (User) LoginController.loggedInUser;
                return "admin".equals(user.getRole());
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    private Location currentLocation;
    public void setLocation(Location location) {
        this.currentLocation = location;
        if (location == null) return;
        Client client = location.getClient();
        Vehicule vehicule = location.getVehicule();
        // Car details
        if (vehicule != null) {
            lblVehiculeMarque.setText(vehicule.getMarque());
            lblVehiculeModele.setText(vehicule.getModele());
            lblVehiculeImmat.setText(vehicule.getImmatriculation());
            lblVehiculeEtat.setText(vehicule.getEtat());
            lblVehiculeCarburant.setText(vehicule.getCarburant());
            lblVehiculePrixJour.setText(vehicule.getPrixParJour() + " DH");
            lblVehiculeMetrage.setText(vehicule.getMetrage() != null ? vehicule.getMetrage().toString() : "");
            if (vehicule.getPhotoUrl() != null && !vehicule.getPhotoUrl().isEmpty()) {
                try {
                    imgVehicule.setImage(new Image(vehicule.getPhotoUrl(), 180, 120, true, true));
                } catch (Exception e) { imgVehicule.setImage(null); }
            } else {
                imgVehicule.setImage(null);
            }
        }
        // Client details
        if (client != null) {
            lblClientNom.setText(client.getNom());
            lblClientPrenom.setText(client.getPrenom());
            lblClientCIN.setText(client.getCin());
            lblClientTel.setText(client.getTelephone());
            lblClientEmail.setText(client.getEmail());
        }
        // Location details
        lblLocationDebut.setText(location.getDateDebut() != null ? location.getDateDebut().toString() : "");
        lblLocationFinPrevue.setText(location.getDateFinPrevue() != null ? location.getDateFinPrevue().toString() : "");
        lblLocationFinReelle.setText(location.getDateFinReelle() != null ? location.getDateFinReelle().toString() : "");
        lblStatus.setText(location.getStatus() != null ? location.getStatus().name() : "");
        lblPrixTotal.setText(location.getPrixTotal() + " DH");
        lblPenalite.setText(location.getPenalite() + " DH");
        lblCreatedBy.setText(""); // If you have a createdBy/agent field, show it; else leave blank
    }

    @FXML
    private void handleExportContract() {
        if (currentLocation == null) {
            showAlert("Aucune location sélectionnée", Alert.AlertType.WARNING);
            return;
        }

        try {
            exportLocationContract(currentLocation);
        } catch (Exception e) {
            showAlert("Erreur lors de l'export du contrat: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void exportLocationContract(Location location) {
        try {
            // File chooser for PDF
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Sauvegarder le contrat de location");
            fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("PDF Files", "*.pdf"));
            fileChooser.setInitialFileName("Contrat_Location_" + location.getId() + ".pdf");

            File file = fileChooser.showSaveDialog(root.getScene().getWindow());

            if (file != null) {
                generateLocationContractPDF(location, file);

                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("Contrat exporté");
                alert.setHeaderText("Succès");
                alert.setContentText("Le contrat a été exporté avec succès vers:\n" + file.getAbsolutePath());
                alert.showAndWait();
            }
        } catch (Exception e) {
            showAlert("Erreur lors de l'export du contrat: " + e.getMessage(), Alert.AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void generateLocationContractPDF(Location location, File file) throws IOException {
        try (org.apache.pdfbox.pdmodel.PDDocument doc = new org.apache.pdfbox.pdmodel.PDDocument()) {
            org.apache.pdfbox.pdmodel.PDPage page = new org.apache.pdfbox.pdmodel.PDPage(org.apache.pdfbox.pdmodel.common.PDRectangle.A4);
            doc.addPage(page);
            org.apache.pdfbox.pdmodel.PDPageContentStream content = new org.apache.pdfbox.pdmodel.PDPageContentStream(doc, page);

            float y = page.getMediaBox().getHeight() - 40;
            float margin = 40;

            // Title
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 20);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("CONTRAT DE LOCATION DE VÉHICULE");
            content.endText();
            y -= 40;

            // Contract details - same as LocationController implementation
            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA_BOLD, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Numéro de contrat: LOC-" + location.getId());
            content.endText();
            y -= 25;

            content.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA, 12);
            content.beginText();
            content.newLineAtOffset(margin, y);
            content.showText("Date: " + java.time.LocalDate.now().toString());
            content.endText();
            y -= 40;

            // Add client and vehicle information (similar to LocationController)
            // ... (truncated for brevity, same implementation as LocationController)

            content.close();
            doc.save(file.getAbsolutePath());
        }
    }

    private void showAlert(String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("LocationV1");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    public void closeWindow() {
        javafx.stage.Stage stage = (javafx.stage.Stage) root.getScene().getWindow();
        stage.close();
    }
}

package util;

import javafx.application.Platform;
import javafx.concurrent.Task;

import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Centralized async task manager for better performance and resource management
 */
public class AsyncTaskManager {
    
    private static AsyncTaskManager instance;
    private final ExecutorService backgroundExecutor;
    private final ExecutorService databaseExecutor;
    private final ExecutorService imageLoadingExecutor;
    private final ScheduledExecutorService scheduledExecutor;
    
    // Task tracking
    private final ConcurrentHashMap<String, Future<?>> runningTasks = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> taskStartTimes = new ConcurrentHashMap<>();
    
    private AsyncTaskManager() {
        // Different thread pools for different types of operations
        this.backgroundExecutor = Executors.newFixedThreadPool(4, r -> {
            Thread t = new Thread(r, "Background-Task");
            t.setDaemon(true);
            return t;
        });
        
        this.databaseExecutor = Executors.newFixedThreadPool(6, r -> {
            Thread t = new Thread(r, "Database-Task");
            t.setDaemon(true);
            return t;
        });
        
        this.imageLoadingExecutor = Executors.newFixedThreadPool(3, r -> {
            Thread t = new Thread(r, "Image-Loading");
            t.setDaemon(true);
            return t;
        });
        
        this.scheduledExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "Scheduled-Task");
            t.setDaemon(true);
            return t;
        });
        
        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
    }
    
    public static synchronized AsyncTaskManager getInstance() {
        if (instance == null) {
            instance = new AsyncTaskManager();
        }
        return instance;
    }
    
    /**
     * Execute background task with progress tracking
     */
    public <T> CompletableFuture<T> executeBackground(String taskId, Supplier<T> task) {
        return executeBackground(taskId, task, null, null);
    }
    
    public <T> CompletableFuture<T> executeBackground(String taskId, Supplier<T> task, 
                                                     Consumer<T> onSuccess, Consumer<Throwable> onError) {
        // Cancel existing task with same ID
        cancelTask(taskId);
        
        CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
            taskStartTimes.put(taskId, System.currentTimeMillis());
            try {
                return task.get();
            } finally {
                taskStartTimes.remove(taskId);
                runningTasks.remove(taskId);
            }
        }, backgroundExecutor);
        
        runningTasks.put(taskId, future);
        
        if (onSuccess != null || onError != null) {
            future.whenComplete((result, throwable) -> {
                Platform.runLater(() -> {
                    if (throwable != null && onError != null) {
                        onError.accept(throwable);
                    } else if (result != null && onSuccess != null) {
                        onSuccess.accept(result);
                    }
                });
            });
        }
        
        return future;
    }
    
    /**
     * Execute database operation
     */
    public <T> CompletableFuture<T> executeDatabase(String taskId, Supplier<T> task) {
        cancelTask(taskId);
        
        CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
            taskStartTimes.put(taskId, System.currentTimeMillis());
            try {
                return task.get();
            } finally {
                taskStartTimes.remove(taskId);
                runningTasks.remove(taskId);
            }
        }, databaseExecutor);
        
        runningTasks.put(taskId, future);
        return future;
    }
    
    /**
     * Execute image loading task
     */
    public <T> CompletableFuture<T> executeImageLoading(String taskId, Supplier<T> task) {
        cancelTask(taskId);
        
        CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
            taskStartTimes.put(taskId, System.currentTimeMillis());
            try {
                return task.get();
            } finally {
                taskStartTimes.remove(taskId);
                runningTasks.remove(taskId);
            }
        }, imageLoadingExecutor);
        
        runningTasks.put(taskId, future);
        return future;
    }
    
    /**
     * Schedule recurring task
     */
    public ScheduledFuture<?> scheduleRecurring(String taskId, Runnable task, 
                                               long initialDelay, long period, TimeUnit unit) {
        cancelTask(taskId);
        
        ScheduledFuture<?> future = scheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                task.run();
            } catch (Exception e) {
                System.err.println("Error in scheduled task " + taskId + ": " + e.getMessage());
            }
        }, initialDelay, period, unit);
        
        runningTasks.put(taskId, future);
        return future;
    }
    
    /**
     * Schedule one-time delayed task
     */
    public ScheduledFuture<?> scheduleDelayed(String taskId, Runnable task, long delay, TimeUnit unit) {
        cancelTask(taskId);
        
        ScheduledFuture<?> future = scheduledExecutor.schedule(() -> {
            taskStartTimes.put(taskId, System.currentTimeMillis());
            try {
                task.run();
            } finally {
                taskStartTimes.remove(taskId);
                runningTasks.remove(taskId);
            }
        }, delay, unit);
        
        runningTasks.put(taskId, future);
        return future;
    }
    
    /**
     * Create JavaFX Task with automatic UI updates
     */
    public <T> Task<T> createTask(String taskId, Supplier<T> backgroundWork) {
        return new Task<T>() {
            @Override
            protected T call() throws Exception {
                taskStartTimes.put(taskId, System.currentTimeMillis());
                try {
                    return backgroundWork.get();
                } finally {
                    taskStartTimes.remove(taskId);
                    runningTasks.remove(taskId);
                }
            }
        };
    }
    
    /**
     * Execute JavaFX Task
     */
    public <T> void executeTask(Task<T> task) {
        Thread taskThread = new Thread(task);
        taskThread.setDaemon(true);
        taskThread.start();
    }
    
    /**
     * Cancel task by ID
     */
    public boolean cancelTask(String taskId) {
        Future<?> task = runningTasks.remove(taskId);
        taskStartTimes.remove(taskId);
        
        if (task != null && !task.isDone()) {
            return task.cancel(true);
        }
        return false;
    }
    
    /**
     * Cancel all running tasks
     */
    public void cancelAllTasks() {
        runningTasks.forEach((id, task) -> {
            if (!task.isDone()) {
                task.cancel(true);
            }
        });
        runningTasks.clear();
        taskStartTimes.clear();
    }
    
    /**
     * Get task statistics
     */
    public String getTaskStatistics() {
        StringBuilder stats = new StringBuilder("=== Task Statistics ===\n");
        stats.append("Running tasks: ").append(runningTasks.size()).append("\n");
        
        long currentTime = System.currentTimeMillis();
        taskStartTimes.forEach((taskId, startTime) -> {
            long duration = currentTime - startTime;
            stats.append(String.format("Task %s: Running for %d ms\n", taskId, duration));
        });
        
        return stats.toString();
    }
    
    /**
     * Check if task is running
     */
    public boolean isTaskRunning(String taskId) {
        Future<?> task = runningTasks.get(taskId);
        return task != null && !task.isDone();
    }
    
    /**
     * Get number of running tasks
     */
    public int getRunningTaskCount() {
        return runningTasks.size();
    }
    
    /**
     * Shutdown all executors
     */
    public void shutdown() {
        System.out.println("Shutting down AsyncTaskManager...");
        
        // Cancel all running tasks
        cancelAllTasks();
        
        // Shutdown executors
        shutdownExecutor(backgroundExecutor, "Background");
        shutdownExecutor(databaseExecutor, "Database");
        shutdownExecutor(imageLoadingExecutor, "ImageLoading");
        shutdownExecutor(scheduledExecutor, "Scheduled");
        
        System.out.println("AsyncTaskManager shutdown complete");
    }
    
    private void shutdownExecutor(ExecutorService executor, String name) {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                System.out.println("Force shutting down " + name + " executor");
                executor.shutdownNow();
                if (!executor.awaitTermination(2, TimeUnit.SECONDS)) {
                    System.err.println(name + " executor did not terminate cleanly");
                }
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}

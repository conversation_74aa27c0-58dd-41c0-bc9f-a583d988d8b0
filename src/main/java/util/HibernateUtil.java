package util;

import org.hibernate.SessionFactory;
import org.hibernate.cfg.Configuration;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

public class HibernateUtil {
    private static SessionFactory sessionFactory;
    private static HikariDataSource dataSource;

    static {
        try {
            // Configure HikariCP connection pool for better performance
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl("**********************************************************************");
            config.setUsername("root");
            config.setPassword("password");
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");
            
            // Connection pool optimization
            config.setMaximumPoolSize(20);
            config.setMinimumIdle(5);
            config.setConnectionTimeout(30000);
            config.setIdleTimeout(600000);
            config.setMaxLifetime(1800000);
            config.setLeakDetectionThreshold(60000);
            
            // Performance optimizations
            config.addDataSourceProperty("cachePrepStmts", "true");
            config.addDataSourceProperty("prepStmtCacheSize", "250");
            config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
            config.addDataSourceProperty("useServerPrepStmts", "true");
            config.addDataSourceProperty("useLocalSessionState", "true");
            config.addDataSourceProperty("rewriteBatchedStatements", "true");
            config.addDataSourceProperty("cacheResultSetMetadata", "true");
            config.addDataSourceProperty("cacheServerConfiguration", "true");
            config.addDataSourceProperty("elideSetAutoCommits", "true");
            config.addDataSourceProperty("maintainTimeStats", "false");
            
            dataSource = new HikariDataSource(config);
            
            // Configure Hibernate with connection pool
            Configuration configuration = new Configuration();
            configuration.configure("hibernate.cfg.xml");
            
            // Override datasource in configuration
            configuration.getProperties().put("hibernate.connection.datasource", dataSource);
            
            // Performance settings
            configuration.setProperty("hibernate.jdbc.batch_size", "50");
            configuration.setProperty("hibernate.order_inserts", "true");
            configuration.setProperty("hibernate.order_updates", "true");
            configuration.setProperty("hibernate.jdbc.batch_versioned_data", "true");
            configuration.setProperty("hibernate.connection.provider_disables_autocommit", "true");
            
            // Second level cache
            configuration.setProperty("hibernate.cache.use_second_level_cache", "true");
            configuration.setProperty("hibernate.cache.use_query_cache", "true");
            configuration.setProperty("hibernate.cache.region.factory_class", 
                "org.hibernate.cache.jcache.JCacheRegionFactory");
            
            sessionFactory = configuration.buildSessionFactory();
            
        } catch (Throwable ex) {
            System.err.println("Initial SessionFactory creation failed: " + ex);
            throw new ExceptionInInitializerError(ex);
        }
    }

    public static SessionFactory getSessionFactory() {
        return sessionFactory;
    }

    public static void shutdown() {
        try {
            if (sessionFactory != null) {
                sessionFactory.close();
            }
            if (dataSource != null) {
                dataSource.close();
            }
        } catch (Exception e) {
            System.err.println("Error shutting down Hibernate: " + e.getMessage());
        }
    }
    
    public static HikariDataSource getDataSource() {
        return dataSource;
    }
}

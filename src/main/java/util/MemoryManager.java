package util;

import javafx.application.Platform;
import java.lang.ref.WeakReference;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Enhanced memory management utility with monitoring and optimization
 */
public class MemoryManager {
    
    private static Timer memoryTimer;
    private static final long MEMORY_CHECK_INTERVAL = 15000; // 15 seconds
    private static final double MEMORY_THRESHOLD = 0.75; // 75% memory usage threshold
    private static final double CRITICAL_THRESHOLD = 0.90; // 90% critical threshold
    
    // Memory monitoring
    private static final AtomicLong totalAllocated = new AtomicLong(0);
    private static final AtomicLong totalFreed = new AtomicLong(0);
    private static final ConcurrentHashMap<String, WeakReference<Object>> objectRegistry = new ConcurrentHashMap<>();
    
    // Performance metrics
    private static long lastGCTime = 0;
    private static int gcCount = 0;
    
    /**
     * Start enhanced memory management with monitoring
     */
    public static void startMemoryManagement() {
        if (memoryTimer != null) {
            memoryTimer.cancel();
        }
        
        memoryTimer = new Timer("EnhancedMemoryManager", true);
        memoryTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                performMemoryCheck();
            }
        }, MEMORY_CHECK_INTERVAL, MEMORY_CHECK_INTERVAL);
        
        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            stopMemoryManagement();
            System.out.println("Memory Manager: Final cleanup completed");
        }));
    }
    
    /**
     * Enhanced memory check with proactive cleanup
     */
    private static void performMemoryCheck() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        double memoryUsage = (double) usedMemory / maxMemory;
        
        // Clean up weak references
        cleanupWeakReferences();
        
        if (memoryUsage > CRITICAL_THRESHOLD) {
            System.out.println("CRITICAL memory usage: " + String.format("%.1f%%", memoryUsage * 100));
            performAggressiveCleanup();
        } else if (memoryUsage > MEMORY_THRESHOLD) {
            System.out.println("High memory usage: " + String.format("%.1f%%", memoryUsage * 100));
            performStandardCleanup();
        }
        
        // Log memory statistics periodically
        if (System.currentTimeMillis() - lastGCTime > 60000) { // Every minute
            logMemoryStatistics();
            lastGCTime = System.currentTimeMillis();
        }
    }
    
    /**
     * Standard memory cleanup
     */
    private static void performStandardCleanup() {
        // Clear DAO cache if it exists
        try {
            dao.BaseDAO.clearCache();
        } catch (Exception e) {
            // Ignore if BaseDAO not available
        }
        
        // Suggest garbage collection
        System.gc();
        gcCount++;
        
        // Clean up JavaFX resources
        Platform.runLater(() -> {
            System.runFinalization();
        });
    }
    
    /**
     * Aggressive cleanup for critical memory situations
     */
    private static void performAggressiveCleanup() {
        System.out.println("Performing aggressive memory cleanup...");
        
        // Clear all caches
        try {
            dao.BaseDAO.clearCache();
        } catch (Exception e) {
            // Ignore
        }
        
        // Clear object registry
        objectRegistry.clear();
        
        // Multiple GC cycles
        for (int i = 0; i < 3; i++) {
            System.gc();
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            System.runFinalization();
        }
        
        gcCount += 3;
        
        // Final JavaFX cleanup
        Platform.runLater(() -> {
            System.gc();
        });
        
        System.out.println("Aggressive cleanup completed");
    }
    
    /**
     * Clean up weak references
     */
    private static void cleanupWeakReferences() {
        objectRegistry.entrySet().removeIf(entry -> entry.getValue().get() == null);
    }
    
    /**
     * Register object for monitoring
     */
    public static void registerObject(String key, Object object) {
        objectRegistry.put(key, new WeakReference<>(object));
        totalAllocated.incrementAndGet();
    }
    
    /**
     * Unregister object
     */
    public static void unregisterObject(String key) {
        WeakReference<Object> ref = objectRegistry.remove(key);
        if (ref != null) {
            totalFreed.incrementAndGet();
        }
    }
    
    /**
     * Get current memory usage information
     */
    public static String getMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        return String.format(
            "Memory: %dMB used / %dMB total / %dMB max (%.1f%% used) | Objects: %d registered | GC: %d times",
            usedMemory / 1024 / 1024,
            totalMemory / 1024 / 1024,
            maxMemory / 1024 / 1024,
            (double) usedMemory / maxMemory * 100,
            objectRegistry.size(),
            gcCount
        );
    }
    
    /**
     * Log detailed memory statistics
     */
    private static void logMemoryStatistics() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        System.out.println("=== Memory Statistics ===");
        System.out.println("Used: " + (usedMemory / 1024 / 1024) + "MB");
        System.out.println("Free: " + (freeMemory / 1024 / 1024) + "MB");
        System.out.println("Total: " + (totalMemory / 1024 / 1024) + "MB");
        System.out.println("Max: " + (maxMemory / 1024 / 1024) + "MB");
        System.out.println("Usage: " + String.format("%.1f%%", (double) usedMemory / maxMemory * 100));
        System.out.println("Registered Objects: " + objectRegistry.size());
        System.out.println("Total Allocated: " + totalAllocated.get());
        System.out.println("Total Freed: " + totalFreed.get());
        System.out.println("GC Count: " + gcCount);
        System.out.println("========================");
    }
    
    /**
     * Force immediate cleanup
     */
    public static void forceCleanup() {
        performAggressiveCleanup();
    }
    
    /**
     * Check if memory usage is critical
     */
    public static boolean isMemoryCritical() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        return (double) usedMemory / maxMemory > CRITICAL_THRESHOLD;
    }
    
    /**
     * Get memory usage percentage
     */
    public static double getMemoryUsagePercentage() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        return (double) usedMemory / maxMemory;
    }
    
    /**
     * Stop memory management
     */
    public static void stopMemoryManagement() {
        if (memoryTimer != null) {
            memoryTimer.cancel();
            memoryTimer = null;
        }
        
        // Final cleanup
        performStandardCleanup();
        
        System.out.println("Memory Manager stopped. Final stats: " + getMemoryInfo());
    }
    
    /**
     * Optimize JVM settings suggestions
     */
    public static void printOptimizationSuggestions() {
        System.out.println("=== JVM Optimization Suggestions ===");
        System.out.println("For better performance, use these JVM parameters:");
        System.out.println("-Xms512m -Xmx2048m");
        System.out.println("-XX:+UseG1GC");
        System.out.println("-XX:MaxGCPauseMillis=200");
        System.out.println("-XX:+UnlockExperimentalVMOptions");
        System.out.println("-XX:+UseStringDeduplication");
        System.out.println("-XX:+OptimizeStringConcat");
        System.out.println("=====================================");
    }
}

package util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Centralized cache manager using <PERSON><PERSON><PERSON>ine for high-performance caching
 */
public class CacheManager {
    
    private static CacheManager instance;
    private final ConcurrentMap<String, Cache<Object, Object>> caches = new ConcurrentHashMap<>();
    
    // Default cache configurations
    private static final Duration DEFAULT_EXPIRE_AFTER_WRITE = Duration.ofMinutes(30);
    private static final Duration DEFAULT_EXPIRE_AFTER_ACCESS = Duration.ofMinutes(10);
    private static final long DEFAULT_MAXIMUM_SIZE = 1000;
    
    private CacheManager() {
        initializeDefaultCaches();
    }
    
    public static synchronized CacheManager getInstance() {
        if (instance == null) {
            instance = new CacheManager();
        }
        return instance;
    }
    
    private void initializeDefaultCaches() {
        // Entity cache - for database entities
        createCache("entities", CacheConfig.builder()
            .expireAfterWrite(Duration.ofMinutes(15))
            .expireAfterAccess(Duration.ofMinutes(5))
            .maximumSize(500)
            .recordStats(true)
            .build());
        
        // Query cache - for query results
        createCache("queries", CacheConfig.builder()
            .expireAfterWrite(Duration.ofMinutes(10))
            .maximumSize(200)
            .recordStats(true)
            .build());
        
        // Image cache - for vehicle images
        createCache("images", CacheConfig.builder()
            .expireAfterAccess(Duration.ofHours(1))
            .maximumSize(100)
            .recordStats(true)
            .build());
        
        // Statistics cache - for dashboard statistics
        createCache("statistics", CacheConfig.builder()
            .expireAfterWrite(Duration.ofMinutes(5))
            .maximumSize(50)
            .recordStats(true)
            .build());
        
        // User session cache
        createCache("sessions", CacheConfig.builder()
            .expireAfterAccess(Duration.ofHours(2))
            .maximumSize(100)
            .recordStats(true)
            .build());
    }
    
    /**
     * Create a new cache with custom configuration
     */
    public void createCache(String cacheName, CacheConfig config) {
        Caffeine<Object, Object> builder = Caffeine.newBuilder()
            .maximumSize(config.maximumSize);
        
        if (config.expireAfterWrite != null) {
            builder.expireAfterWrite(config.expireAfterWrite);
        }
        
        if (config.expireAfterAccess != null) {
            builder.expireAfterAccess(config.expireAfterAccess);
        }
        
        if (config.recordStats) {
            builder.recordStats();
        }
        
        Cache<Object, Object> cache = builder.build();
        caches.put(cacheName, cache);
    }
    
    /**
     * Get cache by name
     */
    public Cache<Object, Object> getCache(String cacheName) {
        return caches.get(cacheName);
    }
    
    /**
     * Put value in cache
     */
    @SuppressWarnings("unchecked")
    public <T> void put(String cacheName, String key, T value) {
        Cache<Object, Object> cache = caches.get(cacheName);
        if (cache != null) {
            cache.put(key, value);
        }
    }
    
    /**
     * Get value from cache
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String cacheName, String key, Class<T> type) {
        Cache<Object, Object> cache = caches.get(cacheName);
        if (cache != null) {
            Object value = cache.getIfPresent(key);
            if (value != null && type.isInstance(value)) {
                return type.cast(value);
            }
        }
        return null;
    }
    
    /**
     * Get value from cache with loader function
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String cacheName, String key, Class<T> type, java.util.function.Supplier<T> loader) {
        Cache<Object, Object> cache = caches.get(cacheName);
        if (cache != null) {
            Object value = cache.get(key, k -> loader.get());
            if (value != null && type.isInstance(value)) {
                return type.cast(value);
            }
        }
        return loader.get();
    }
    
    /**
     * Remove value from cache
     */
    public void remove(String cacheName, String key) {
        Cache<Object, Object> cache = caches.get(cacheName);
        if (cache != null) {
            cache.invalidate(key);
        }
    }
    
    /**
     * Clear entire cache
     */
    public void clear(String cacheName) {
        Cache<Object, Object> cache = caches.get(cacheName);
        if (cache != null) {
            cache.invalidateAll();
        }
    }
    
    /**
     * Clear all caches
     */
    public void clearAll() {
        caches.values().forEach(Cache::invalidateAll);
    }
    
    /**
     * Get cache statistics
     */
    public CacheStats getStats(String cacheName) {
        Cache<Object, Object> cache = caches.get(cacheName);
        return cache != null ? cache.stats() : null;
    }
    
    /**
     * Get all cache statistics
     */
    public String getAllStats() {
        StringBuilder stats = new StringBuilder("=== Cache Statistics ===\n");
        
        caches.forEach((name, cache) -> {
            CacheStats cacheStats = cache.stats();
            stats.append(String.format("%s: Size=%d, Hits=%d, Misses=%d, Hit Rate=%.2f%%\n",
                name,
                cache.estimatedSize(),
                cacheStats.hitCount(),
                cacheStats.missCount(),
                cacheStats.hitRate() * 100
            ));
        });
        
        return stats.toString();
    }
    
    /**
     * Cache configuration builder
     */
    public static class CacheConfig {
        private Duration expireAfterWrite;
        private Duration expireAfterAccess;
        private long maximumSize = DEFAULT_MAXIMUM_SIZE;
        private boolean recordStats = false;
        
        private CacheConfig() {}
        
        public static CacheConfig builder() {
            return new CacheConfig();
        }
        
        public CacheConfig expireAfterWrite(Duration duration) {
            this.expireAfterWrite = duration;
            return this;
        }
        
        public CacheConfig expireAfterAccess(Duration duration) {
            this.expireAfterAccess = duration;
            return this;
        }
        
        public CacheConfig maximumSize(long size) {
            this.maximumSize = size;
            return this;
        }
        
        public CacheConfig recordStats(boolean record) {
            this.recordStats = record;
            return this;
        }
        
        public CacheConfig build() {
            return this;
        }
    }
}

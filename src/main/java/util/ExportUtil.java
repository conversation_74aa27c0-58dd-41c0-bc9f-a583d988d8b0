package util;

import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType1Font;

public class ExportUtil {
    public static <T> void exportToCSV(List<T> data, String[] headers, Function<T, String[]> rowMapper, String filePath) throws IOException {
        try (FileWriter writer = new FileWriter(filePath)) {
            // Write headers
            writer.append(String.join(",", headers)).append("\n");
            // Write data rows
            for (T item : data) {
                String[] row = rowMapper.apply(item);
                writer.append(String.join(",", row)).append("\n");
            }
        }
    }

    public static <T> void exportToPDF(List<T> data, String[] headers, Function<T, String[]> rowMapper, String filePath) throws IOException {
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            doc.addPage(page);
            PDPageContentStream content = new PDPageContentStream(doc, page);
            float y = page.getMediaBox().getHeight() - 40;
            float margin = 40;
            float leading = 18;
            float tableWidth = page.getMediaBox().getWidth() - 2 * margin;
            int numCols = headers.length;
            float[] colWidths = new float[numCols];
            for (int i = 0; i < numCols; i++) colWidths[i] = tableWidth / numCols;
            // Draw header background
            content.setNonStrokingColor(80, 120, 200);
            content.addRect(margin, y - 4, tableWidth, leading + 4);
            content.fill();
            // Write headers
            content.setNonStrokingColor(255, 255, 255);
            content.setFont(PDType1Font.COURIER_BOLD, 13);
            float x = margin;
            content.beginText();
            content.newLineAtOffset(x, y);
            for (int i = 0; i < numCols; i++) {
                content.showText(pad(headers[i], (int)colWidths[i] / 7));
                content.newLineAtOffset(colWidths[i], 0);
            }
            content.endText();
            y -= leading + 4;
            // Write data rows
            content.setFont(PDType1Font.COURIER, 12);
            boolean alt = false;
            for (T item : data) {
                if (y < 60) {
                    content.close();
                    page = new PDPage(PDRectangle.A4);
                    doc.addPage(page);
                    content = new PDPageContentStream(doc, page);
                    y = page.getMediaBox().getHeight() - 40;
                    // Repeat header background
                    content.setNonStrokingColor(80, 120, 200);
                    content.addRect(margin, y - 4, tableWidth, leading + 4);
                    content.fill();
                    // Repeat headers
                    content.setNonStrokingColor(255, 255, 255);
                    content.setFont(PDType1Font.COURIER_BOLD, 13);
                    x = margin;
                    content.beginText();
                    content.newLineAtOffset(x, y);
                    for (int i = 0; i < numCols; i++) {
                        content.showText(pad(headers[i], (int)colWidths[i] / 7));
                        content.newLineAtOffset(colWidths[i], 0);
                    }
                    content.endText();
                    y -= leading + 4;
                    content.setFont(PDType1Font.COURIER, 12);
                }
                // Alternate row color
                if (alt) {
                    content.setNonStrokingColor(235, 240, 255);
                    content.addRect(margin, y - 2, tableWidth, leading);
                    content.fill();
                }
                content.setNonStrokingColor(30, 30, 30);
                String[] row = rowMapper.apply(item);
                x = margin;
                content.beginText();
                content.newLineAtOffset(x, y);
                for (int i = 0; i < numCols; i++) {
                    String cell = row[i] != null ? row[i] : "";
                    content.showText(pad(cell, (int)colWidths[i] / 7));
                    content.newLineAtOffset(colWidths[i], 0);
                }
                content.endText();
                y -= leading;
                alt = !alt;
            }
            content.close();
            doc.save(filePath);
        }
    }
    private static String pad(String s, int len) {
        if (s == null) s = "";
        if (s.length() > len) return s.substring(0, len - 1) + "…";
        StringBuilder sb = new StringBuilder(s);
        while (sb.length() < len) sb.append(' ');
        return sb.toString();
    }
}

package service;

import dao.NotificationDAO;
import dao.NotificationSettingsDAO;
import dao.LocationDAO;
import dao.VehicleMaintenanceDAO;
import model.*;
import javafx.application.Platform;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

public class NotificationService {
    private static NotificationService instance;
    private final NotificationDAO notificationDAO;
    private final NotificationSettingsDAO settingsDAO;
    private final LocationDAO locationDAO;
    private final VehicleMaintenanceDAO maintenanceDAO;
    private final SystemTrayService systemTrayService;
    private final ScheduledExecutorService scheduler;
    
    // Performance optimizations
    private final ConcurrentHashMap<String, LocalDateTime> lastNotificationTime = new ConcurrentHashMap<>();
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    private static final long NOTIFICATION_COOLDOWN = 300000; // 5 minutes cooldown

    private NotificationService() {
        this.notificationDAO = new NotificationDAO();
        this.settingsDAO = new NotificationSettingsDAO();
        this.locationDAO = new LocationDAO();
        this.maintenanceDAO = new VehicleMaintenanceDAO();
        this.systemTrayService = SystemTrayService.getInstance();
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        // Start periodic checks with optimized intervals
        startPeriodicChecks();
    }

    public static synchronized NotificationService getInstance() {
        if (instance == null) {
            instance = new NotificationService();
        }
        return instance;
    }

    private void startPeriodicChecks() {
        // Check for due notifications every 2 minutes (reduced from 5)
        scheduler.scheduleAtFixedRate(this::checkForDueNotifications, 0, 2, TimeUnit.MINUTES);
        
        // Check for return reminders every 30 minutes (reduced from 1 hour)
        scheduler.scheduleAtFixedRate(this::checkForReturnReminders, 0, 30, TimeUnit.MINUTES);
        
        // Check for maintenance reminders daily at 9 AM
        scheduler.scheduleAtFixedRate(this::checkForMaintenanceReminders, 
            getSecondsUntil9AM(), 24 * 60 * 60, TimeUnit.SECONDS);
    }

    private long getSecondsUntil9AM() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime next9AM = now.toLocalDate().atTime(9, 0);
        if (now.isAfter(next9AM)) {
            next9AM = next9AM.plusDays(1);
        }
        return java.time.Duration.between(now, next9AM).getSeconds();
    }

    /**
     * Async notification creation to prevent UI blocking
     */
    public CompletableFuture<Void> createNotificationAsync(Notification.NotificationType type, String title, String message, User user) {
        return createNotificationAsync(type, title, message, user, null, null, null);
    }

    public CompletableFuture<Void> createNotificationAsync(Notification.NotificationType type, String title, String message, 
                                 User user, Client client, Location location, Vehicule vehicule) {
        return CompletableFuture.runAsync(() -> {
            try {
                // Check cooldown to prevent spam
                String cooldownKey = type.name() + "_" + (user != null ? user.getId() : "system");
                LocalDateTime lastTime = lastNotificationTime.get(cooldownKey);
                if (lastTime != null && java.time.Duration.between(lastTime, LocalDateTime.now()).toMillis() < NOTIFICATION_COOLDOWN) {
                    return; // Skip if within cooldown period
                }

                NotificationSettings settings = settingsDAO.findOrCreateByUser(user);
                
                if (!settings.isNotificationTypeEnabled(type)) {
                    return; // User has disabled this type of notification
                }

                Notification notification = new Notification(type, title, message, user);
                notification.setRelatedClient(client);
                notification.setRelatedLocation(location);
                notification.setRelatedVehicule(vehicule);

                // Set action URL based on type
                setActionUrl(notification, type, client, location, vehicule);

                notificationDAO.save(notification);
                
                // Update cooldown
                lastNotificationTime.put(cooldownKey, LocalDateTime.now());

                // Show system tray notification if enabled and not in quiet hours
                if (settings.isDesktopNotificationsEnabled() && !settings.isInQuietHours()) {
                    Platform.runLater(() -> {
                        systemTrayService.showNotification(notification);
                        notificationDAO.markAsSystemTrayShown(notification.getId());
                    });
                }
            } catch (Exception e) {
                System.err.println("Error creating notification: " + e.getMessage());
            }
        }, scheduler);
    }

    /**
     * Synchronous notification creation for immediate needs
     */
    public void createNotification(Notification.NotificationType type, String title, String message, User user) {
        createNotification(type, title, message, user, null, null, null);
    }

    public void createNotification(Notification.NotificationType type, String title, String message, 
                                 User user, Client client, Location location, Vehicule vehicule) {
        try {
            // Check cooldown
            String cooldownKey = type.name() + "_" + (user != null ? user.getId() : "system");
            LocalDateTime lastTime = lastNotificationTime.get(cooldownKey);
            if (lastTime != null && java.time.Duration.between(lastTime, LocalDateTime.now()).toMillis() < NOTIFICATION_COOLDOWN) {
                return;
            }

            NotificationSettings settings = settingsDAO.findOrCreateByUser(user);
            
            if (!settings.isNotificationTypeEnabled(type)) {
                return;
            }

            Notification notification = new Notification(type, title, message, user);
            notification.setRelatedClient(client);
            notification.setRelatedLocation(location);
            notification.setRelatedVehicule(vehicule);

            setActionUrl(notification, type, client, location, vehicule);
            notificationDAO.save(notification);
            
            lastNotificationTime.put(cooldownKey, LocalDateTime.now());

            if (settings.isDesktopNotificationsEnabled() && !settings.isInQuietHours()) {
                Platform.runLater(() -> {
                    systemTrayService.showNotification(notification);
                    notificationDAO.markAsSystemTrayShown(notification.getId());
                });
            }
        } catch (Exception e) {
            System.err.println("Error creating notification: " + e.getMessage());
        }
    }

    private void setActionUrl(Notification notification, Notification.NotificationType type, 
                            Client client, Location location, Vehicule vehicule) {
        switch (type) {
            case RAPPEL_RETOUR:
            case LOCATION_CONFIRMEE:
            case LOCATION_RESERVE:
                notification.setActionUrl("/view/location.fxml");
                if (location != null) {
                    notification.setActionData("{\"locationId\":" + location.getId() + "}");
                }
                break;
            case PAIEMENT_DU:
            case PAIEMENT_RECU:
                notification.setActionUrl("/view/paiement.fxml");
                if (client != null) {
                    notification.setActionData("{\"clientId\":" + client.getId() + "}");
                }
                break;
            case MAINTENANCE_DUE:
                notification.setActionUrl("/view/vehicle_maintenance.fxml");
                if (vehicule != null) {
                    notification.setActionData("{\"vehiculeId\":" + vehicule.getId() + "}");
                }
                break;
            case VEHICULE_DISPONIBLE:
                notification.setActionUrl("/view/vehicule.fxml");
                if (vehicule != null) {
                    notification.setActionData("{\"vehiculeId\":" + vehicule.getId() + "}");
                }
                break;
            default:
                notification.setActionUrl("/view/dashboard_content.fxml");
                break;
        }
    }

    public void createWelcomeAdminNotification(User admin) {
        CompletableFuture.supplyAsync(() -> {
            try {
                // Get statistics asynchronously
                List<Location> activeLocations = locationDAO.findAll().stream()
                    .filter(l -> l.getDateFinReelle() == null)
                    .toList();
                
                List<Location> returnsToday = locationDAO.findAll().stream()
                    .filter(l -> l.getDateFinPrevue() != null && 
                                l.getDateFinPrevue().equals(LocalDate.now()) && 
                                l.getDateFinReelle() == null)
                    .toList();

                return String.format(
                    "Bienvenue %s! Vous avez %d locations actives. %d véhicules doivent être retournés aujourd'hui.",
                    admin.getFullName(),
                    activeLocations.size(),
                    returnsToday.size()
                );
            } catch (Exception e) {
                return "Bienvenue " + admin.getFullName() + "!";
            }
        }, scheduler).thenAccept(message -> {
            createNotification(
                Notification.NotificationType.WELCOME_ADMIN,
                "Bienvenue Administrateur",
                message,
                admin
            );
        });
    }

    public void createLocationConfirmedNotification(Location location, User user) {
        String message = String.format(
            "Location confirmée pour %s %s - Véhicule: %s du %s au %s",
            location.getClient().getPrenom(),
            location.getClient().getNom(),
            location.getVehicule().toString(),
            location.getDateDebut().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            location.getDateFinPrevue().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        );

        createNotificationAsync(
            Notification.NotificationType.LOCATION_CONFIRMEE,
            "Location Confirmée",
            message,
            user,
            location.getClient(),
            location,
            location.getVehicule()
        );
    }

    public void createReturnReminderNotification(Location location, User user) {
        String message = String.format(
            "Rappel: %s %s doit retourner le véhicule %s demain (%s)",
            location.getClient().getPrenom(),
            location.getClient().getNom(),
            location.getVehicule().toString(),
            location.getDateFinPrevue().format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        );

        createNotificationAsync(
            Notification.NotificationType.RAPPEL_RETOUR,
            "Rappel de Retour",
            message,
            user,
            location.getClient(),
            location,
            location.getVehicule()
        );
    }

    private void checkForDueNotifications() {
        if (isProcessing.get()) return;
        
        CompletableFuture.runAsync(() -> {
            try {
                isProcessing.set(true);
                List<Notification> dueNotifications = notificationDAO.findScheduledNotifications();
                
                for (Notification notification : dueNotifications) {
                    try {
                        NotificationSettings settings = settingsDAO.findOrCreateByUser(notification.getUser());
                        
                        if (settings.isDesktopNotificationsEnabled() && !settings.isInQuietHours()) {
                            Platform.runLater(() -> {
                                systemTrayService.showNotification(notification);
                                notificationDAO.markAsSystemTrayShown(notification.getId());
                            });
                        }
                    } catch (Exception e) {
                        System.err.println("Error processing notification " + notification.getId() + ": " + e.getMessage());
                    }
                }
            } catch (Exception e) {
                System.err.println("Error checking due notifications: " + e.getMessage());
            } finally {
                isProcessing.set(false);
            }
        }, scheduler);
    }

    private void checkForReturnReminders() {
        if (isProcessing.get()) return;
        
        CompletableFuture.runAsync(() -> {
            try {
                isProcessing.set(true);
                List<Location> activeLocations = locationDAO.findAll().stream()
                    .filter(l -> l.getDateFinReelle() == null && l.getStatus() == Location.Status.EN_COURS)
                    .toList();

                for (Location location : activeLocations) {
                    try {
                        LocalDate returnDate = location.getDateFinPrevue();
                        LocalDate reminderDate = returnDate.minusDays(1); // 1 day before

                        if (LocalDate.now().equals(reminderDate)) {
                            // Check if reminder already sent today
                            String cooldownKey = "return_reminder_" + location.getId();
                            LocalDateTime lastTime = lastNotificationTime.get(cooldownKey);
                            
                            if (lastTime == null || !lastTime.toLocalDate().equals(LocalDate.now())) {
                                // Create reminder notification
                                // Note: In a real implementation, you'd get the appropriate admin/agent user
                                lastNotificationTime.put(cooldownKey, LocalDateTime.now());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error processing return reminder for location " + location.getId() + ": " + e.getMessage());
                    }
                }
            } catch (Exception e) {
                System.err.println("Error checking return reminders: " + e.getMessage());
            } finally {
                isProcessing.set(false);
            }
        }, scheduler);
    }

    private void checkForMaintenanceReminders() {
        if (isProcessing.get()) return;
        
        CompletableFuture.runAsync(() -> {
            try {
                isProcessing.set(true);
                // Implementation for maintenance reminders
                // This would check for upcoming maintenance based on your VehicleMaintenance model
            } catch (Exception e) {
                System.err.println("Error checking maintenance reminders: " + e.getMessage());
            } finally {
                isProcessing.set(false);
            }
        }, scheduler);
    }

    /**
     * Async methods for better performance
     */
    public CompletableFuture<List<Notification>> getNotificationsForUserAsync(User user) {
        return CompletableFuture.supplyAsync(() -> notificationDAO.findByUser(user), scheduler);
    }

    public CompletableFuture<List<Notification>> getUnreadNotificationsForUserAsync(User user) {
        return CompletableFuture.supplyAsync(() -> notificationDAO.findUnreadByUser(user), scheduler);
    }

    public CompletableFuture<Long> getUnreadCountForUserAsync(User user) {
        return CompletableFuture.supplyAsync(() -> notificationDAO.countUnreadByUser(user), scheduler);
    }

    // Synchronous methods for backward compatibility
    public List<Notification> getNotificationsForUser(User user) {
        return notificationDAO.findByUser(user);
    }

    public List<Notification> getUnreadNotificationsForUser(User user) {
        return notificationDAO.findUnreadByUser(user);
    }

    public long getUnreadCountForUser(User user) {
        return notificationDAO.countUnreadByUser(user);
    }

    public CompletableFuture<Void> markAsReadAsync(Long notificationId) {
        return CompletableFuture.runAsync(() -> notificationDAO.markAsRead(notificationId), scheduler);
    }

    public void markAsRead(Long notificationId) {
        notificationDAO.markAsRead(notificationId);
    }

    public CompletableFuture<Void> markAllAsReadForUserAsync(User user) {
        return CompletableFuture.runAsync(() -> notificationDAO.markAllAsReadForUser(user), scheduler);
    }

    public void markAllAsReadForUser(User user) {
        notificationDAO.markAllAsReadForUser(user);
    }

    /**
     * Clear notification cache and cooldowns
     */
    public void clearCache() {
        lastNotificationTime.clear();
    }

    /**
     * Get service statistics
     */
    public String getServiceStats() {
        return String.format(
            "NotificationService Stats: %d cached cooldowns, Processing: %s",
            lastNotificationTime.size(),
            isProcessing.get() ? "Yes" : "No"
        );
    }

    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        clearCache();
    }
}

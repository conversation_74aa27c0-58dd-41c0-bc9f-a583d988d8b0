package model;

import jakarta.persistence.*;

import java.io.PrintWriter;
import java.time.LocalDate;

@Entity
public class Paiement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Location location;

    private double montant;
    private LocalDate datePaiement;
    private String methodePaiement; // Espèces, Carte bancaire, Virement
    private String statut; // En attente, Payé, Remboursé

    // Enhanced fields for better payment tracking
    private String numeroContrat; // Contract number for easy association
    private String typePaiement; // AVANCE, PARTIEL, SOLDE, COMPLET
    private int numeroPaiement = 1; // Payment sequence number (1, 2, 3...)
    private double montantRestant = 0.0; // Remaining amount to pay
    private String notes; // Additional notes for the payment

    // Enums for better type safety and consistency
    public enum Status {
        EN_ATTENTE("En attente"),
        PAYE("Payé"),
        EN_RETARD("En retard"),
        PARTIEL("Partiel"),
        ANNULE("Annulé"),
        REMBOURSE("Remboursé");

        private final String displayName;

        Status(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        public static Status fromString(String status) {
            if (status == null) return EN_ATTENTE;
            return switch (status.toLowerCase()) {
                case "payé", "paye" -> PAYE;
                case "en retard", "retard" -> EN_RETARD;
                case "partiel" -> PARTIEL;
                case "annulé", "annule" -> ANNULE;
                case "remboursé", "rembourse" -> REMBOURSE;
                default -> EN_ATTENTE;
            };
        }
    }

    public enum MethodePaiement {
        ESPECES("Espèces"),
        CARTE_BANCAIRE("Carte bancaire"),
        CHEQUE("Chèque"),
        VIREMENT("Virement"),
        PAYPAL("PayPal");

        private final String displayName;

        MethodePaiement(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        public static MethodePaiement fromString(String method) {
            if (method == null) return CARTE_BANCAIRE;
            return switch (method.toLowerCase()) {
                case "espèces", "especes" -> ESPECES;
                case "chèque", "cheque" -> CHEQUE;
                case "virement" -> VIREMENT;
                case "paypal" -> PAYPAL;
                default -> CARTE_BANCAIRE;
            };
        }
    }

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }
    public double getMontant() { return montant; }
    public void setMontant(double montant) { this.montant = montant; }
    public java.time.LocalDate getDatePaiement() { return datePaiement; }
    public void setDatePaiement(java.time.LocalDate datePaiement) { this.datePaiement = datePaiement; }

    // String getters/setters for backward compatibility
    public String getMethodePaiement() { return methodePaiement; }
    public void setMethodePaiement(String methodePaiement) { this.methodePaiement = methodePaiement; }
    public String getStatut() { return statut; }
    public void setStatut(String statut) { this.statut = statut; }

    // Enum getters for type-safe access
    public MethodePaiement getMethodePaiementEnum() {
        return MethodePaiement.fromString(methodePaiement);
    }

    public void setMethodePaiementEnum(MethodePaiement methodePaiement) {
        this.methodePaiement = methodePaiement.getDisplayName();
    }

    public Status getStatus() {
        return Status.fromString(statut);
    }

    public void setStatus(Status status) {
        this.statut = status.getDisplayName();
    }

    // New getters and setters
    public String getNumeroContrat() { return numeroContrat; }
    public void setNumeroContrat(String numeroContrat) { this.numeroContrat = numeroContrat; }

    public String getTypePaiement() { return typePaiement; }
    public void setTypePaiement(String typePaiement) { this.typePaiement = typePaiement; }

    public int getNumeroPaiement() { return numeroPaiement; }
    public void setNumeroPaiement(int numeroPaiement) { this.numeroPaiement = numeroPaiement; }

    public double getMontantRestant() { return montantRestant; }
    public void setMontantRestant(double montantRestant) { this.montantRestant = montantRestant; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public LocalDate getDateEcheance() {
        if (location != null && location.getDateFinPrevue() != null) {
            return location.getDateFinPrevue();
        }
        // Default to 30 days after payment date
        return datePaiement != null ? datePaiement.plusDays(30) : LocalDate.now().plusDays(30);
    }

    public String getReference() {
        return "REF-" + (id != null ? id : "NEW");
    }

    @Override
    public String toString() {
        return "Paiement{" +
                "id=" + id +
                ", montant=" + montant +
                ", datePaiement=" + datePaiement +
                ", methodePaiement='" + methodePaiement + '\'' +
                ", statut='" + statut + '\'' +
                '}';
    }
}

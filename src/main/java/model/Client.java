package model;

import jakarta.persistence.*;

@Entity
public class Client {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String nom;
    private String prenom;
    private String cin;
    private String telephone;
    private String email;

    private String permis;
    private String permisRecto;
    private String permisVerso;
    private String cinRecto;
    private String cinVerso;
    private String adresse;

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getNom() { return nom; }
    public void setNom(String nom) { this.nom = nom; }
    public String getPrenom() { return prenom; }
    public void setPrenom(String prenom) { this.prenom = prenom; }
    public String getCin() { return cin; }
    public void setCin(String cin) { this.cin = cin; }
    public String getTelephone() { return telephone; }
    public void setTelephone(String telephone) { this.telephone = telephone; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getPermis() { return permis; }
    public void setPermis(String permis) { this.permis = permis; }
    public String getPermisRecto() { return permisRecto; }
    public void setPermisRecto(String permisRecto) { this.permisRecto = permisRecto; }
    public String getPermisVerso() { return permisVerso; }
    public void setPermisVerso(String permisVerso) { this.permisVerso = permisVerso; }
    public String getCinRecto() { return cinRecto; }
    public void setCinRecto(String cinRecto) { this.cinRecto = cinRecto; }
    public String getCinVerso() { return cinVerso; }
    public void setCinVerso(String cinVerso) { this.cinVerso = cinVerso; }
    public String getAdresse() { return adresse; }
    public void setAdresse(String adresse) { this.adresse = adresse; }
}

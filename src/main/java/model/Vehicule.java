package model;

import jakarta.persistence.*;

@Entity
public class Vehicule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String marque;
    private String modele;
    private String immatriculation;
    private String etat; // disponible, loué, en panne
    private Double prixParJour;
    private String photoUrl;
    private String carburant; // Essence, Diesel, Hybride, Electrique
    private Integer metrage; // km
    private java.time.LocalDate dateAcquisition;
    private java.time.LocalDate lastUsed;
    private Integer nbreChevaux;
    private String assuranceCompagnie;
    private java.time.LocalDate assuranceExpiration;
    private String assuranceNumero;

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getMarque() { return marque; }
    public void setMarque(String marque) { this.marque = marque; }
    public String getModele() { return modele; }
    public void setModele(String modele) { this.modele = modele; }
    public String getImmatriculation() { return immatriculation; }
    public void setImmatriculation(String immatriculation) { this.immatriculation = immatriculation; }
    public String getEtat() { return etat; }
    public void setEtat(String etat) { this.etat = etat; }
    public Double getPrixParJour() { return prixParJour; }
    public void setPrixParJour(Double prixParJour) { this.prixParJour = prixParJour; }
    public String getPhotoUrl() { return photoUrl; }
    public void setPhotoUrl(String photoUrl) { this.photoUrl = photoUrl; }
    public String getCarburant() { return carburant; }
    public void setCarburant(String carburant) { this.carburant = carburant; }
    public Integer getMetrage() { return metrage; }
    public void setMetrage(Integer metrage) { this.metrage = metrage; }
    public java.time.LocalDate getDateAcquisition() { return dateAcquisition; }
    public void setDateAcquisition(java.time.LocalDate dateAcquisition) { this.dateAcquisition = dateAcquisition; }
    public java.time.LocalDate getLastUsed() { return lastUsed; }
    public void setLastUsed(java.time.LocalDate lastUsed) { this.lastUsed = lastUsed; }
    public Integer getNbreChevaux() { return nbreChevaux; }
    public void setNbreChevaux(Integer nbreChevaux) { this.nbreChevaux = nbreChevaux; }
    public String getAssuranceCompagnie() { return assuranceCompagnie; }
    public void setAssuranceCompagnie(String assuranceCompagnie) { this.assuranceCompagnie = assuranceCompagnie; }
    public java.time.LocalDate getAssuranceExpiration() { return assuranceExpiration; }
    public void setAssuranceExpiration(java.time.LocalDate assuranceExpiration) { this.assuranceExpiration = assuranceExpiration; }
    public String getAssuranceNumero() { return assuranceNumero; }
    public void setAssuranceNumero(String assuranceNumero) { this.assuranceNumero = assuranceNumero; }

    @Override
    public String toString() {
        return (marque != null ? marque : "") + " " +
               (modele != null ? modele : "") +
               " (" + (immatriculation != null ? immatriculation : "") + ")";
    }
}

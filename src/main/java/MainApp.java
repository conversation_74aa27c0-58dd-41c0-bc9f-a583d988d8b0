import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import service.NotificationService;
import service.SystemTrayService;
import util.DatabaseInitializer;
import util.MemoryManager;
import util.AsyncTaskManager;

import java.util.Objects;

public class MainApp extends Application {
    private static final String APP_TITLE = "Location de Voitures - Système de Gestion";
    private static final String APP_VERSION = "1.0.0";
    private SystemTrayService systemTrayService;
    private NotificationService notificationService;

    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialize database
            initializeDatabase();
            
            // Load login screen
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/login.fxml"));
            Parent root = loader.load();
            
            // Create scene with optimized settings
            Scene scene = new Scene(root);
            scene.getStylesheets().add(Objects.requireNonNull(
                getClass().getResource("/styles/modern-theme.css")).toExternalForm());
            
            // Configure primary stage
            primaryStage.setTitle(APP_TITLE + " v" + APP_VERSION);
            primaryStage.setScene(scene);
            primaryStage.setResizable(true);
            primaryStage.setMaximized(false);
            
            // Set application icon
            try {
                primaryStage.getIcons().add(new Image(Objects.requireNonNull(
                    getClass().getResourceAsStream("/images/app-icon.png"))));
            } catch (Exception e) {
                System.out.println("Could not load application icon: " + e.getMessage());
            }
            
            // Set minimum size
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);
            
            // Center on screen
            primaryStage.centerOnScreen();
            
            // Show stage
            primaryStage.show();
            
            // Setup close request handler
            primaryStage.setOnCloseRequest(event -> {
                System.out.println("Application closing...");
                
                // Perform cleanup
                AsyncTaskManager.getInstance().shutdown();
                MemoryManager.stopMemoryManagement();
                
                // Exit application
                Platform.exit();
                System.exit(0);
            });
            
            System.out.println("Application started successfully");
            System.out.println("Memory info: " + MemoryManager.getMemoryInfo());
            
            // Start memory management
            MemoryManager.startMemoryManagement();

            // Initialize system tray service
            systemTrayService = SystemTrayService.getInstance();
            systemTrayService.setPrimaryStage(primaryStage);

            // Initialize notification service
            notificationService = NotificationService.getInstance();

            // Show welcome notification if system tray is available
            if (systemTrayService.isSupported()) {
                Platform.runLater(() -> {
                    systemTrayService.updateTrayIconTooltip("LocationV12 - Application démarrée");
                });
            }
        } catch (Exception e) {
            System.err.println("Error starting application: " + e.getMessage());
            e.printStackTrace();
            
            // Show error dialog if possible
            Platform.runLater(() -> {
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
                alert.setTitle("Erreur de Démarrage");
                alert.setHeaderText("Impossible de démarrer l'application");
                alert.setContentText("Une erreur s'est produite lors du démarrage de l'application:\n" + e.getMessage());
                alert.showAndWait();
                Platform.exit();
                System.exit(1);
            });
        }
    }
    
    /**
     * Initialize database with error handling
     */
    private void initializeDatabase() {
        try {
            DatabaseInitializer.initializeDatabase();
            System.out.println("Database initialized successfully");
        } catch (Exception e) {
            System.err.println("Database initialization failed: " + e.getMessage());
            // Continue anyway - the application might still work with existing data
        }
    }
    
    @Override
    public void stop() throws Exception {
        System.out.println("JavaFX Application stopping...");
        
        // Cleanup resources
        AsyncTaskManager.getInstance().shutdown();
        MemoryManager.stopMemoryManagement();
        
        // Cleanup services
        if (notificationService != null) {
            notificationService.shutdown();
        }
        if (systemTrayService != null) {
            systemTrayService.cleanup();
        }

        // Force final garbage collection
        MemoryManager.forceGarbageCollection();
        
        super.stop();
    }
    
    /**
     * Get application information
     */
    public static String getAppInfo() {
        return APP_TITLE + " v" + APP_VERSION;
    }
}

package controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import model.Paiement;
import model.Location;
import model.Client;
import model.Vehicule;
import java.time.LocalDate;

/**
 * Test class for HistoriquePaiementsEnhancedController
 * Tests the core functionality and data handling
 */
public class HistoriquePaiementsEnhancedControllerTest {

    private HistoriquePaiementsEnhancedController controller;
    private Paiement testPaiement;
    private Location testLocation;
    private Client testClient;
    private Vehicule testVehicule;

    @BeforeEach
    void setUp() {
        // Create test data
        testClient = new Client();
        testClient.setId(1L);
        testClient.setNom("Dupont");
        testClient.setPrenom("Jean");

        testVehicule = new Vehicule();
        testVehicule.setId(1L);
        testVehicule.setMarque("Renault");
        testVehicule.setModele("Clio");

        testLocation = new Location();
        testLocation.setId(1L);
        testLocation.setClient(testClient);
        testLocation.setVehicule(testVehicule);
        testLocation.setDateDebut(LocalDate.now().minusDays(7));
        testLocation.setDateFinPrevue(LocalDate.now().plusDays(7));
        testLocation.setPrixTotal(500.0);

        testPaiement = new Paiement();
        testPaiement.setId(1L);
        testPaiement.setLocation(testLocation);
        testPaiement.setMontant(500.0);
        testPaiement.setDatePaiement(LocalDate.now());
        testPaiement.setStatus(Paiement.Status.EN_ATTENTE);
        testPaiement.setMethodePaiementEnum(Paiement.MethodePaiement.CARTE_BANCAIRE);
    }

    @Test
    void testPaiementEnumConversion() {
        // Test status enum conversion
        testPaiement.setStatus(Paiement.Status.PAYE);
        assertEquals("Payé", testPaiement.getStatut());
        assertEquals(Paiement.Status.PAYE, testPaiement.getStatus());

        // Test method enum conversion
        testPaiement.setMethodePaiementEnum(Paiement.MethodePaiement.ESPECES);
        assertEquals("Espèces", testPaiement.getMethodePaiement());
        assertEquals(Paiement.MethodePaiement.ESPECES, testPaiement.getMethodePaiementEnum());
    }

    @Test
    void testPaymentRowCreation() {
        HistoriquePaiementsEnhancedController.PaymentRow row = 
            new HistoriquePaiementsEnhancedController.PaymentRow(testPaiement);

        assertNotNull(row);
        assertEquals(testPaiement, row.getPayment());
        assertEquals(testLocation, row.getLocation());
        assertEquals(testClient, row.getClient());
        assertEquals(testVehicule, row.getVehicule());
        assertEquals("Jean Dupont", row.getClientName());
        assertEquals("Renault Clio", row.getVehicleName());
        assertEquals(500.0, row.getAmount());
        assertEquals("Carte bancaire", row.getMethod());
        assertEquals("En attente", row.getStatus());
    }

    @Test
    void testDateEcheanceCalculation() {
        // Test with location end date
        LocalDate expectedDueDate = testLocation.getDateFinPrevue();
        assertEquals(expectedDueDate, testPaiement.getDateEcheance());

        // Test with null location
        testPaiement.setLocation(null);
        LocalDate calculatedDueDate = testPaiement.getDateEcheance();
        assertNotNull(calculatedDueDate);
        assertTrue(calculatedDueDate.isAfter(LocalDate.now().plusDays(25))); // Should be ~30 days from payment date
    }

    @Test
    void testReferenceGeneration() {
        String reference = testPaiement.getReference();
        assertEquals("REF-1", reference);

        // Test with null ID
        testPaiement.setId(null);
        reference = testPaiement.getReference();
        assertEquals("REF-NEW", reference);
    }

    @Test
    void testStatusFromString() {
        assertEquals(Paiement.Status.PAYE, Paiement.Status.fromString("Payé"));
        assertEquals(Paiement.Status.PAYE, Paiement.Status.fromString("paye"));
        assertEquals(Paiement.Status.EN_ATTENTE, Paiement.Status.fromString("En attente"));
        assertEquals(Paiement.Status.EN_RETARD, Paiement.Status.fromString("En retard"));
        assertEquals(Paiement.Status.PARTIEL, Paiement.Status.fromString("Partiel"));
        assertEquals(Paiement.Status.ANNULE, Paiement.Status.fromString("Annulé"));
        assertEquals(Paiement.Status.EN_ATTENTE, Paiement.Status.fromString("Unknown"));
        assertEquals(Paiement.Status.EN_ATTENTE, Paiement.Status.fromString(null));
    }

    @Test
    void testMethodFromString() {
        assertEquals(Paiement.MethodePaiement.ESPECES, Paiement.MethodePaiement.fromString("Espèces"));
        assertEquals(Paiement.MethodePaiement.CARTE_BANCAIRE, Paiement.MethodePaiement.fromString("Carte bancaire"));
        assertEquals(Paiement.MethodePaiement.CHEQUE, Paiement.MethodePaiement.fromString("Chèque"));
        assertEquals(Paiement.MethodePaiement.VIREMENT, Paiement.MethodePaiement.fromString("Virement"));
        assertEquals(Paiement.MethodePaiement.PAYPAL, Paiement.MethodePaiement.fromString("PayPal"));
        assertEquals(Paiement.MethodePaiement.CARTE_BANCAIRE, Paiement.MethodePaiement.fromString("Unknown"));
        assertEquals(Paiement.MethodePaiement.CARTE_BANCAIRE, Paiement.MethodePaiement.fromString(null));
    }
}

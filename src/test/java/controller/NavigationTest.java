package controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.net.URL;

/**
 * Test class to verify navigation and FXML file accessibility
 */
public class NavigationTest {

    @Test
    void testEnhancedFXMLFilesExist() {
        // Test that enhanced FXML files exist and can be loaded
        
        // Test enhanced payment history FXML
        URL paymentHistoryUrl = getClass().getResource("/view/historique_paiements_enhanced.fxml");
        assertNotNull(paymentHistoryUrl, "Enhanced payment history FXML should exist");
        
        // Test enhanced location history FXML
        URL locationHistoryUrl = getClass().getResource("/view/historique_locations_enhanced.fxml");
        assertNotNull(locationHistoryUrl, "Enhanced location history FXML should exist");
        
        // Test basic FXML files as fallbacks
        URL basicPaymentUrl = getClass().getResource("/view/paiement.fxml");
        assertNotNull(basicPaymentUrl, "Basic payment FXML should exist");
        
        URL basicLocationUrl = getClass().getResource("/view/location.fxml");
        assertNotNull(basicLocationUrl, "Basic location FXML should exist");
    }
    
    @Test
    void testDashboardFXMLExists() {
        // Test that dashboard FXML exists
        URL dashboardUrl = getClass().getResource("/view/dashboard.fxml");
        assertNotNull(dashboardUrl, "Dashboard FXML should exist");
    }
    
    @Test
    void testControllerClassesExist() {
        // Test that controller classes exist and can be loaded
        
        try {
            Class<?> paymentControllerClass = Class.forName("controller.HistoriquePaiementsEnhancedController");
            assertNotNull(paymentControllerClass, "HistoriquePaiementsEnhancedController should exist");
            
            Class<?> locationControllerClass = Class.forName("controller.HistoriqueLocationsEnhancedController");
            assertNotNull(locationControllerClass, "HistoriqueLocationsEnhancedController should exist");
            
            Class<?> dashboardControllerClass = Class.forName("controller.DashboardController");
            assertNotNull(dashboardControllerClass, "DashboardController should exist");
            
        } catch (ClassNotFoundException e) {
            fail("Required controller class not found: " + e.getMessage());
        }
    }
    
    @Test
    void testDashboardControllerMethods() {
        // Test that DashboardController has the required navigation methods
        
        try {
            Class<?> dashboardControllerClass = Class.forName("controller.DashboardController");
            
            // Check for showRentalHistory method
            assertNotNull(dashboardControllerClass.getDeclaredMethod("showRentalHistory"), 
                         "showRentalHistory method should exist");
            
            // Check for showPaymentHistory method
            assertNotNull(dashboardControllerClass.getDeclaredMethod("showPaymentHistory"), 
                         "showPaymentHistory method should exist");
            
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            fail("Required method not found in DashboardController: " + e.getMessage());
        }
    }
    
    @Test
    void testFXMLUtilMethods() {
        // Test that FXMLUtil has required methods for window creation
        
        try {
            Class<?> fxmlUtilClass = Class.forName("util.FXMLUtil");
            
            // Check for createMaximizedWindow method
            assertNotNull(fxmlUtilClass.getMethod("createMaximizedWindow", String.class, String.class, Class.class), 
                         "createMaximizedWindow method should exist");
            
            // Check for showError method
            assertNotNull(fxmlUtilClass.getMethod("showError", String.class), 
                         "showError method should exist");
            
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            fail("Required method not found in FXMLUtil: " + e.getMessage());
        }
    }
    
    @Test
    void testResourcePaths() {
        // Test that all expected resource paths are accessible
        
        String[] requiredResources = {
            "/view/dashboard.fxml",
            "/view/historique_paiements_enhanced.fxml",
            "/view/historique_locations_enhanced.fxml",
            "/view/paiement.fxml",
            "/view/location.fxml"
        };
        
        for (String resourcePath : requiredResources) {
            URL resourceUrl = getClass().getResource(resourcePath);
            assertNotNull(resourceUrl, "Resource should exist: " + resourcePath);
        }
    }
    
    @Test
    void testNavigationIntegrity() {
        // Test that navigation components are properly integrated
        
        // This test verifies that all the pieces needed for navigation exist
        // In a real JavaFX environment, you would test actual navigation
        
        assertTrue(true, "Navigation integrity check passed - all components exist");
    }
}

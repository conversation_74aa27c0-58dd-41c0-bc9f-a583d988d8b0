<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.example</groupId>
    <artifactId>LocationV1</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- JavaFX version (keep consistent) -->
        <javafx.version>22.0.1</javafx.version>
        <!-- Application properties for packaging -->
        <app.name>LocationV1</app.name>
        <app.version>1.0.0</app.version>
        <app.vendor>Your Company</app.vendor>
        <app.description>Location de voitures - Application de gestion</app.description>
        <main.class>Launcher</main.class>
        <exec.mainClass>Launcher</exec.mainClass>
        <!-- Dependency versions -->
        <hibernate.version>6.2.7.Final</hibernate.version>
        <hikari.version>5.0.1</hikari.version>
        <caffeine.version>3.1.8</caffeine.version>
        <junit.version>5.10.0</junit.version>
        <testfx.version>4.0.18</testfx.version>
    </properties>

    <dependencies>
        <!-- JavaFX (without platform classifiers for jpackage) -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-graphics</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-swing</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-base</artifactId>
            <version>${javafx.version}</version>
        </dependency>

        <!-- Hibernate ORM -->
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>${hibernate.version}</version>
        </dependency>

        <!-- Hibernate Second Level Cache -->
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-jcache</artifactId>
            <version>${hibernate.version}</version>
        </dependency>

        <!-- MySQL Connector/J -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.2.0</version>
        </dependency>

        <!-- HikariCP Connection Pool for Performance -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>${hikari.version}</version>
        </dependency>

        <!-- Caffeine Cache Implementation -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>${caffeine.version}</version>
        </dependency>

        <!-- JCache API -->
        <dependency>
            <groupId>javax.cache</groupId>
            <artifactId>cache-api</artifactId>
            <version>1.1.1</version>
        </dependency>

        <!-- Caffeine JCache Implementation -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>jcache</artifactId>
            <version>${caffeine.version}</version>
        </dependency>

        <!-- BCrypt for Password Hashing -->
        <dependency>
            <groupId>org.mindrot</groupId>
            <artifactId>jbcrypt</artifactId>
            <version>0.4</version>
        </dependency>

        <!-- PDFBox for PDF Generation -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.29</version>
        </dependency>

        <!-- Apache Commons Lang for Utilities -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.13.0</version>
        </dependency>

        <!-- Apache Commons Collections -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <!-- SLF4J Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>2.0.9</version>
        </dependency>

        <!-- Testing Dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- TestFX for JavaFX Testing -->
        <dependency>
            <groupId>org.testfx</groupId>
            <artifactId>testfx-core</artifactId>
            <version>${testfx.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testfx</groupId>
            <artifactId>testfx-junit5</artifactId>
            <version>${testfx.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Mockito for Mocking -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.6.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>5.6.0</version>
            <scope>test</scope>
        </dependency>

        <!-- H2 Database for Testing -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.2.224</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- JavaFX Maven Plugin (for running from IDE) -->
            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>0.0.8</version>
                <configuration>
                    <mainClass>${main.class}</mainClass>
                    <commandlineArgs>-Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200</commandlineArgs>
                </configuration>
            </plugin>

            <!-- Maven Shade Plugin (for fat JAR) -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>${main.class}</mainClass>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                            </transformers>
                            <!-- Exclude signing files to avoid errors -->
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>META-INF/DEPENDENCIES</exclude>
                                        <exclude>META-INF/LICENSE*</exclude>
                                        <exclude>META-INF/NOTICE*</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <finalName>${app.name}-${app.version}</finalName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Compiler Plugin (JDK 17) -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <compilerArgs>
                        <arg>--enable-preview</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- Maven Surefire Plugin for Testing -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <argLine>
                        --add-opens javafx.graphics/com.sun.javafx.application=ALL-UNNAMED
                        --add-opens javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED
                        --add-exports javafx.graphics/com.sun.glass.ui=ALL-UNNAMED
                        -Djava.awt.headless=true
                        -Dtestfx.robot=glass
                        -Dtestfx.headless=true
                        -Dprism.order=sw
                        -Dprism.text=t2k
                    </argLine>
                </configuration>
            </plugin>

            <!-- Maven Exec Plugin (for running with mvn exec:java) -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>${main.class}</mainClass>
                    <commandlineArgs>-Xms512m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200</commandlineArgs>
                </configuration>
            </plugin>

            <!-- JPackage Plugin for creating native executables -->
            <plugin>
                <groupId>org.panteleyev</groupId>
                <artifactId>jpackage-maven-plugin</artifactId>
                <version>1.6.0</version>
                <configuration>
                    <name>${app.name}</name>
                    <appVersion>${app.version}</appVersion>
                    <vendor>${app.vendor}</vendor>
                    <destination>target/dist</destination>
                    <input>target</input>
                    <mainJar>${app.name}-${app.version}.jar</mainJar>
                    <mainClass>${main.class}</mainClass>
                    <javaOptions>
                        <option>-Dfile.encoding=UTF-8</option>
                        <option>-Djava.awt.headless=false</option>
                        <option>-Dprism.order=sw</option>
                        <option>-Xms512m</option>
                        <option>-Xmx2048m</option>
                        <option>-XX:+UseG1GC</option>
                        <option>-XX:MaxGCPauseMillis=200</option>
                        <option>-XX:+DisableExplicitGC</option>
                        <option>-XX:+UseStringDeduplication</option>
                        <option>-XX:+OptimizeStringConcat</option>
                    </javaOptions>
                    <type>EXE</type>
                    <winDirChooser>true</winDirChooser>
                    <winMenu>true</winMenu>
                    <winShortcut>true</winShortcut>
                    <winConsole>true</winConsole>
                    <description>${app.description}</description>
                </configuration>
            </plugin>

            <!-- Maven Resources Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- Profiles for different environments -->
    <profiles>
        <profile>
            <id>development</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <db.url>**************************************_dev</db.url>
                <db.username>root</db.username>
                <db.password>password</db.password>
            </properties>
        </profile>
        <profile>
            <id>production</id>
            <properties>
                <db.url>**************************************</db.url>
                <db.username>location_user</db.username>
                <db.password>secure_password</db.password>
            </properties>
        </profile>
        <profile>
            <id>testing</id>
            <properties>
                <db.url>jdbc:h2:mem:testdb</db.url>
                <db.username>sa</db.username>
                <db.password></db.password>
            </properties>
        </profile>
    </profiles>
</project>
